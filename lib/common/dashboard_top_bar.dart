import 'package:flutter/material.dart';
import 'package:orange_ui/utils/asset_res.dart';
import 'package:orange_ui/utils/color_res.dart';

class DashboardTopBar extends StatelessWidget {
  final VoidCallback onNotificationTap;
  final VoidCallback? onTitleTap;
  final int? isDating;

  const DashboardTopBar({
    super.key,
    required this.onNotificationTap,
    this.onTitleTap,
    required this.isDating,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 5),
      child: Row(
        children: [
          InkWell(
            onTap: onTitleTap,
            child: Image.asset(AssetRes.themeLabel, height: 28),
          ),
          const Spacer(),
          if (isDating == 1)
            RoundedImage(onTap: onNotificationTap, image: AssetRes.bell),
        ],
      ),
    );
  }
}

class RoundedImage extends StatelessWidget {
  final VoidCallback onTap;
  final String image;

  const RoundedImage({super.key, required this.onTap, required this.image});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(20),
      onTap: onTap,
      child: Container(
        height: 37,
        width: 37,
        margin: const EdgeInsets.symmetric(horizontal: 3),
        decoration: BoxDecoration(
          color: ColorRes.darkBlue.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: Center(
          child: Image.asset(
            image,
            height: 20,
            width: 20,
            color: ColorRes.darkBlue,
          ),
        ),
      ),
    );
  }
}
