import 'package:flutter/material.dart';
import 'package:orange_ui/model/user/registration_user.dart';

class SearchFilter {
  String genderPreference;
  double distanceKm;
  String distanceUnit; // 'km' or 'miles'
  String? selectedRegion;
  String? selectedCountry;
  RangeValues ageRange;
  List<String> selectedInterests;

  SearchFilter({
    this.genderPreference = 'Both',
    this.distanceKm = 50.0,
    this.distanceUnit = 'km',
    this.selectedRegion,
    this.selectedCountry,
    this.ageRange = const RangeValues(18, 35),
    this.selectedInterests = const [],
  });

  SearchFilter copyWith({
    String? genderPreference,
    double? distanceKm,
    String? distanceUnit,
    String? selectedRegion,
    String? selectedCountry,
    RangeValues? ageRange,
    List<String>? selectedInterests,
  }) {
    return SearchFilter(
      genderPreference: genderPreference ?? this.genderPreference,
      distanceKm: distanceKm ?? this.distanceKm,
      distanceUnit: distanceUnit ?? this.distanceUnit,
      selectedRegion: selectedRegion ?? this.selectedRegion,
      selectedCountry: selectedCountry ?? this.selectedCountry,
      ageRange: ageRange ?? this.ageRange,
      selectedInterests: selectedInterests ?? this.selectedInterests,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'genderPreference': genderPreference,
      'distanceKm': distanceKm,
      'distanceUnit': distanceUnit,
      'selectedRegion': selectedRegion,
      'selectedCountry': selectedCountry,
      'ageRangeStart': ageRange.start,
      'ageRangeEnd': ageRange.end,
      'selectedInterests': selectedInterests,
    };
  }

  factory SearchFilter.fromJson(Map<String, dynamic> json) {
    return SearchFilter(
      genderPreference: json['genderPreference'] ?? 'Both',
      distanceKm: (json['distanceKm'] ?? 50.0).toDouble(),
      distanceUnit: json['distanceUnit'] ?? 'km',
      selectedRegion: json['selectedRegion'],
      selectedCountry: json['selectedCountry'],
      ageRange: RangeValues(
        (json['ageRangeStart'] ?? 18.0).toDouble(),
        (json['ageRangeEnd'] ?? 35.0).toDouble(),
      ),
      selectedInterests: List<String>.from(json['selectedInterests'] ?? []),
    );
  }
}

class RegionData {
  final String name;
  final List<String> countries;

  RegionData({required this.name, required this.countries});
}

class DummyProfileData {
  final int id;
  final String name;
  final int age;
  final String gender;
  final String imageUrl;
  final String location;
  final String country;
  final String region;
  final String bio;
  final List<String> interests;
  final double distanceKm;
  final bool isVerified;

  DummyProfileData({
    required this.id,
    required this.name,
    required this.age,
    required this.gender,
    required this.imageUrl,
    required this.location,
    required this.country,
    required this.region,
    required this.bio,
    required this.interests,
    required this.distanceKm,
    this.isVerified = false,
  });

  // Convert to RegistrationUserData for compatibility
  RegistrationUserData toRegistrationUserData() {
    return RegistrationUserData(
      id: id,
      fullname: name,
      age: age,
      gender:
          gender == 'Male'
              ? 1
              : gender == 'Female'
              ? 2
              : 3,
      latitude: '0.0', // Dummy latitude
      longitude: '0.0', // Dummy longitude
      about: bio,
      isVerified: isVerified ? 2 : 1,
      images: [Images(image: imageUrl)],
    );
  }
}
