class SubscriptionPlan {
  final String title;
  final String? price;
  final String? duration;
  final List<String> features;
  final bool isPopular;
  final bool isFree;
  final String? productId;

  SubscriptionPlan({
    required this.title,
    this.price,
    this.duration,
    required this.features,
    this.isPopular = false,
    this.isFree = false,
    this.productId,
  });

  static List<SubscriptionPlan> getDefaultPlans() {
    return [
      SubscriptionPlan(
        title: 'Free Plan',
        isFree: true,
        features: [
          'Unlimited messaging',
          '10 Likes per day',
          '× No phone or video chat',
        ],
      ),
      SubscriptionPlan(
        title: 'Gold Plan',
        price: '\$24.99',
        duration: 'month',
        isPopular: true,
        productId: 'gold_plan_monthly',
        features: [
          'Unlimited messaging',
          '40 Likes per day',
          '5 Super Likes per week',
          '500 minutes/month total audio call',
          '500 minutes/month total video call',
        ],
      ),
      SubscriptionPlan(
        title: 'Platinum Plan',
        price: '\$59.99',
        duration: 'month',
        productId: 'platinum_plan_monthly',
        features: [
          'Unlimited messaging',
          'Unlimited Likes',
          '10 Super Likes per week',
          '2,000 minutes/month total audio call',
          '2,000 minutes/month total video call',
        ],
      ),
    ];
  }
}
