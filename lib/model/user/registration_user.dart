// ignore_for_file: unnecessary_getters_setters

class RegistrationUser {
  RegistrationUser({
    bool? status,
    String? message,
    RegistrationUserData? data,
  }) {
    _status = status;
    _message = message;
    _data = data;
  }

  RegistrationUser.fromJson(dynamic json) {
    _status = json['status'];
    _message = json['message'];
    _data =
        json['data'] != null
            ? RegistrationUserData.fromJson(json['data'])
            : null;
  }

  bool? _status;
  String? _message;
  RegistrationUserData? _data;

  RegistrationUser copyWith({
    bool? status,
    String? message,
    RegistrationUserData? data,
  }) => RegistrationUser(
    status: status ?? _status,
    message: message ?? _message,
    data: data ?? _data,
  );

  bool? get status => _status;

  String? get message => _message;

  RegistrationUserData? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['status'] = _status;
    map['message'] = _message;
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    return map;
  }
}

class RegistrationUserData {
  RegistrationUserData({
    int? id,
    int? isBlock,
    int? gender,
    int? genderPreferred,
    int? agePreferredMin,
    int? agePreferredMax,
    String? savedProfile,
    String? likedProfile,
    String? interests,
    int? age,
    String? identity,
    String? fullname,
    String? instagram,
    String? youtube,
    String? facebook,
    String? live,
    String? bio,
    String? about,
    String? latitude,
    String? longitude,
    int? loginType,
    String? deviceToken,
    String? blockedUsers,
    int? totalCollected,
    int? following,
    int? followers,
    int? followingStatus,
    int? totalStreams,
    int? deviceType,
    int? isNotification,
    int? isVerified,
    int? showOnMap,
    int? anonymous,
    int? isVideoCall,
    int? canGoLive,
    int? isLiveNow,
    int? isFake,
    String? password,
    bool? isLiked,

    List<Images>? images,
  }) {
    _id = id;
    _isBlock = isBlock;
    _gender = gender;
    _genderPreferred = genderPreferred;
    _agePreferredMin = agePreferredMin;
    _agePreferredMax = agePreferredMax;
    _savedProfile = savedProfile;
    _likedProfile = likedProfile;
    _interests = interests;
    _age = age;
    _identity = identity;
    _fullname = fullname;
    _username = username;
    _instagram = instagram;
    _youtube = youtube;
    _facebook = facebook;
    _live = live;
    _bio = bio;
    _about = about;
    _following = following;
    _followers = followers;
    _followingStatus = followingStatus;
    _latitude = latitude;
    _longitude = longitude;
    _loginType = loginType;
    _deviceToken = deviceToken;
    _blockedUsers = blockedUsers;
    _deviceType = deviceType;
    _isNotification = isNotification;
    _isVerified = isVerified;
    _showOnMap = showOnMap;
    _anonymous = anonymous;
    _isVideoCall = isVideoCall;
    _canGoLive = canGoLive;
    _isLiveNow = isLiveNow;
    _isFake = isFake;
    _password = password;

    _isLiked = isLiked;
    _images = images ?? [];
  }

  RegistrationUserData.fromJson(dynamic json) {
    _id = json['id'];
    _isBlock = json['is_block'];
    _gender = json['gender'];
    _genderPreferred = json['gender_preferred'];
    _agePreferredMin = json['age_preferred_min'];
    _agePreferredMax = json['age_preferred_max'];
    _savedProfile = json['savedprofile'];
    _likedProfile = json['likedprofile'];
    _interests = json['interests'];
    _age = json['age'];
    _identity = json['identity'];
    _fullname = json['fullname'];
    _username = json['username'];
    _instagram = json['instagram'];
    _youtube = json['youtube'];
    _facebook = json['facebook'];
    _following = json['following'];
    _followers = json['followers'];
    _followingStatus = json['followingStatus'];
    _live = json['live'];
    _bio = json['bio'];
    _about = json['about'];
    _latitude = json['lattitude'];
    _longitude = json['longitude'];
    _loginType = json['login_type'];
    _deviceToken = json['device_token'];
    _blockedUsers = json['blocked_users'];
    _totalStreams = json['total_streams'];
    _deviceType = json['device_type'];
    _isNotification = json['is_notification'];
    _isVerified = json['is_verified'];
    _showOnMap = json['show_on_map'];
    _anonymous = json['anonymous'];
    _isVideoCall = json['is_video_call'];
    _canGoLive = json['can_go_live'];
    _isLiveNow = json['is_live_now'];
    _isFake = json['is_fake'];
    _isLiked = json['is_like'];
    _password = json['password'];

    if (json['images'] != null) {
      _images = [];
      json['images'].forEach((v) {
        _images.add(Images.fromJson(v));
      });
    }
  }

  int? _id;
  int? _isBlock;
  int? _gender;
  int? _genderPreferred;
  int? _agePreferredMin;
  int? _agePreferredMax;
  String? _savedProfile;
  String? _likedProfile;
  String? _interests;
  int? _age;
  String? _identity;
  String? _fullname;
  String? _username;
  String? _instagram;
  String? _youtube;
  String? _facebook;
  String? _live;
  String? _bio;
  String? _about;
  String? _latitude;
  String? _longitude;
  int? _loginType;
  int? _following;
  int? _followers;
  int? _followingStatus;
  String? _deviceToken;
  String? _blockedUsers;
  // int? _wallet;
  // int? _totalCollected;
  int? _totalStreams;
  int? _deviceType;
  int? _isNotification;
  int? _isVerified;
  int? _showOnMap;
  int? _anonymous;
  int? _isVideoCall;
  int? _canGoLive;
  int? _isLiveNow;
  int? _isFake;
  String? _password;
  bool? _isLiked;

  List<Images> _images = [];

  int? get id => _id;

  int? get isBlock => _isBlock;

  int? get gender => _gender;

  int? get genderPreferred => _genderPreferred;

  int? get agePreferredMin => _agePreferredMin;

  int? get agePreferredMax => _agePreferredMax;

  String? get savedProfile => _savedProfile;

  String? get likedProfile => _likedProfile;

  String? get interests => _interests;

  int? get age => _age;
  int? get following => _following;
  int? get followers => _followers;
  int? get followingStatus => _followingStatus;

  String? get identity => _identity;

  String? get fullname => _fullname;
  String? get username => _username;

  String? get instagram => _instagram;

  String? get youtube => _youtube;

  String? get facebook => _facebook;

  String? get live => _live;

  String? get bio => _bio;

  String? get about => _about;

  String? get latitude => _latitude;

  String? get longitude => _longitude;

  int? get loginType => _loginType;

  String? get deviceToken => _deviceToken;

  String? get blockedUsers => _blockedUsers;


  int? get totalStreams => _totalStreams;

  int? get deviceType => _deviceType;

  int? get isNotification => _isNotification;

  int? get isVerified => _isVerified;

  int? get showOnMap => _showOnMap;

  int? get anonymous => _anonymous;

  int? get isVideoCall => _isVideoCall;

  int? get canGoLive => _canGoLive;

  set canGoLive(int? value) {
    _canGoLive = value;
  }

  int? get isLiveNow => _isLiveNow;

  set isLiveNow(int? value) {
    _isLiveNow = value;
  }

  int? get isFake => _isFake;
  bool? get isLiked => _isLiked;

  set isLiked(bool? value) {
    _isLiked = value;
  }

  String? get password => _password;

  List<Images> get images => _images;

  void followerCount(int value) {
    _followers = (followers ?? 0) + value;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['is_block'] = _isBlock;
    map['gender'] = _gender;
    map['gender_preferred'] = _genderPreferred;
    map['age_preferred_min'] = _agePreferredMin;
    map['age_preferred_max'] = _agePreferredMax;
    map['savedprofile'] = _savedProfile;
    map['likedprofile'] = _likedProfile;
    map['interests'] = _interests;
    map['age'] = _age;
    map['identity'] = _identity;
    map['fullname'] = _fullname;
    map['username'] = _username;
    map['instagram'] = _instagram;
    map['youtube'] = _youtube;
    map['facebook'] = _facebook;
    map['live'] = _live;
    map['bio'] = _bio;
    map['following'] = _following;
    map['followers'] = _followers;
    map['followingStatus'] = _followingStatus;
    map['about'] = _about;
    map['lattitude'] = _latitude;
    map['longitude'] = _longitude;
    map['login_type'] = _loginType;
    map['device_token'] = _deviceToken;
    map['blocked_users'] = _blockedUsers;
    // map['wallet'] = _wallet;
    // map['total_collected'] = _totalCollected;
    map['total_streams'] = _totalStreams;
    map['device_type'] = _deviceType;
    map['is_notification'] = _isNotification;
    map['is_verified'] = _isVerified;
    map['show_on_map'] = _showOnMap;
    map['anonymous'] = _anonymous;
    map['is_video_call'] = _isVideoCall;
    map['can_go_live'] = _canGoLive;
    map['is_live_now'] = _isLiveNow;
    map['is_fake'] = _isFake;
    map['password'] = _password;
    map['is_like'] = _isLiked;

    if (_images.isNotEmpty) {
      map['images'] = _images.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

class Images {
  Images({int? id, int? userId, String? image}) {
    _id = id;
    _userId = userId;
    _image = image;
  }

  Images.fromJson(dynamic json) {
    _id = json['id'];
    _userId = json['user_id'];
    _image = json['image'];
  }

  int? _id;
  int? _userId;
  String? _image;

  Images copyWith({int? id, int? userId, String? image}) =>
      Images(id: id ?? _id, userId: userId ?? _userId, image: image ?? _image);

  int? get id => _id;

  int? get userId => _userId;

  String? get image => _image;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['user_id'] = _userId;
    map['image'] = _image;
    return map;
  }
}

class Interest {
  Interest({int? id, String? title, String? image}) {
    _id = id;
    _title = title;
    _image = image;
  }

  Interest.fromJson(dynamic json) {
    _id = json['id'];
    _title = json['title'];
    _image = json['image'];
  }

  int? _id;
  String? _title;
  String? _image;

  Interest copyWith({int? id, String? title, String? image}) =>
      Interest(id: id ?? _id, title: title ?? _title, image: image ?? _image);

  int? get id => _id;

  String? get title => _title;

  String? get image => _image;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = _id;
    map['title'] = _title;
    map['image'] = _image;
    return map;
  }
}
