import 'package:cloud_firestore/cloud_firestore.dart';

class CallData {
  String? _callId;
  String? _callerId;
  String? _callerName;
  String? _callerImage;
  String? _receiverId;
  String? _receiverName;
  String? _receiverImage;
  String? _channelId;
  String? _agoraToken;
  int? _callType; // 0 = audio, 1 = video
  int?
  _callStatus; // 0 = calling, 1 = answered, 2 = ended, 3 = missed, 4 = rejected
  DateTime? _startTime;
  DateTime? _endTime;
  int? _duration; // in seconds
  bool? _isIncoming;

  CallData({
    String? callId,
    String? callerId,
    String? callerName,
    String? callerImage,
    String? receiverId,
    String? receiverName,
    String? receiverImage,
    String? channelId,
    String? agoraToken,
    int? callType,
    int? callStatus,
    DateTime? startTime,
    DateTime? endTime,
    int? duration,
    bool? isIncoming,
  }) {
    _callId = callId;
    _callerId = callerId;
    _callerName = callerName;
    _callerImage = callerImage;
    _receiverId = receiverId;
    _receiverName = receiverName;
    _receiverImage = receiverImage;
    _channelId = channelId;
    _agoraToken = agoraToken;
    _callType = callType;
    _callStatus = callStatus;
    _startTime = startTime;
    _endTime = endTime;
    _duration = duration;
    _isIncoming = isIncoming;
  }

  CallData.fromJson(Map<String, dynamic> json) {
    _callId = json['callId'];
    _callerId = json['callerId'];
    _callerName = json['callerName'];
    _callerImage = json['callerImage'];
    _receiverId = json['receiverId'];
    _receiverName = json['receiverName'];
    _receiverImage = json['receiverImage'];
    _channelId = json['channelId'];
    _agoraToken = json['agoraToken'];
    _callType = json['callType'];
    _callStatus = json['callStatus'];
    _startTime =
        json['startTime'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['startTime'])
            : null;
    _endTime =
        json['endTime'] != null
            ? DateTime.fromMillisecondsSinceEpoch(json['endTime'])
            : null;
    _duration = json['duration'];
    _isIncoming = json['isIncoming'];
  }

  Map<String, dynamic> toJson() {
    return {
      'callId': _callId,
      'callerId': _callerId,
      'callerName': _callerName,
      'callerImage': _callerImage,
      'receiverId': _receiverId,
      'receiverName': _receiverName,
      'receiverImage': _receiverImage,
      'channelId': _channelId,
      'agoraToken': _agoraToken,
      'callType': _callType,
      'callStatus': _callStatus,
      'startTime': _startTime?.millisecondsSinceEpoch,
      'endTime': _endTime?.millisecondsSinceEpoch,
      'duration': _duration,
      'isIncoming': _isIncoming,
    };
  }

  Map<String, dynamic> toFirestore() {
    return toJson();
  }

  static CallData fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> snapshot,
    SnapshotOptions? options,
  ) {
    final data = snapshot.data();
    return CallData.fromJson(data ?? {});
  }

  // Getters
  String? get callId => _callId;
  String? get callerId => _callerId;
  String? get callerName => _callerName;
  String? get callerImage => _callerImage;
  String? get receiverId => _receiverId;
  String? get receiverName => _receiverName;
  String? get receiverImage => _receiverImage;
  String? get channelId => _channelId;
  String? get agoraToken => _agoraToken;
  int? get callType => _callType;
  int? get callStatus => _callStatus;
  DateTime? get startTime => _startTime;
  DateTime? get endTime => _endTime;
  int? get duration => _duration;
  bool? get isIncoming => _isIncoming;

  // Setters
  set callStatus(int? status) => _callStatus = status;
  set endTime(DateTime? time) => _endTime = time;
  set duration(int? dur) => _duration = dur;
  set startTime(DateTime? time) => _startTime = time;
  set isIncoming(bool? incoming) => _isIncoming = incoming;

  // Helper methods
  bool get isVideoCall => _callType == 1;
  bool get isAudioCall => _callType == 0;
  bool get isMissedCall => _callStatus == 3;
  bool get isAnsweredCall => _callStatus == 1;
  bool get isRejectedCall => _callStatus == 4;
  bool get isEndedCall => _callStatus == 2;

  String get callTypeString => isVideoCall ? 'Video Call' : 'Audio Call';

  String get callStatusString {
    switch (_callStatus) {
      case 0:
        return 'Calling';
      case 1:
        return 'Answered';
      case 2:
        return 'Ended';
      case 3:
        return 'Missed';
      case 4:
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  String get formattedDuration {
    if (_duration == null || _duration == 0) return '00:00';

    int minutes = _duration! ~/ 60;
    int seconds = _duration! % 60;

    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

// Call status constants
class CallStatus {
  static const int calling = 0;
  static const int answered = 1;
  static const int ended = 2;
  static const int missed = 3;
  static const int rejected = 4;
}

// Call type constants
class CallType {
  static const int audio = 0;
  static const int video = 1;
}
