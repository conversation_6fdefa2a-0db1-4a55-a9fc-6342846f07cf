import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:orange_ui/utils/app_res.dart';
import 'package:stacked/stacked.dart';

class EditProfileScreenViewModel extends BaseViewModel {
  void init() {
    ensureDummyInterests();
    getLocalData();
    getInterestApiCall();
  }

  RegistrationUserData? userData;

  EditProfileScreenViewModel(this.userData);

  TextEditingController fullNameController = TextEditingController();
  TextEditingController userNameController = TextEditingController();
  TextEditingController addressController = TextEditingController();
  TextEditingController bioController = TextEditingController();
  TextEditingController aboutController = TextEditingController();
  TextEditingController ageController = TextEditingController();
  TextEditingController instagramController = TextEditingController();
  TextEditingController facebookController = TextEditingController();
  TextEditingController youtubeController = TextEditingController();
  RangeValues currentRangeValues = const RangeValues(
    AppRes.ageMin,
    AppRes.ageMax,
  );

  FocusNode ageFocus = FocusNode();
  FocusNode bioFocus = FocusNode();
  FocusNode aboutFocus = FocusNode();
  FocusNode addressFocus = FocusNode();
  FocusNode youtubeFocus = FocusNode();
  FocusNode facebookFocus = FocusNode();
  FocusNode fullNameFocus = FocusNode();
  FocusNode userNameFocus = FocusNode();
  FocusNode instagramFocus = FocusNode();

  List<Interest> hobbiesList = [];
  List<String> selectedList = [];

  String gender = AppRes.female;
  String fullNameError = '';
  String userNameError = '';
  String bioError = '';
  String aboutError = '';
  String addressError = '';
  String ageError = '';

  List<String> deleteIds = [];
  List<String> interestList = [];
  List<Images> imageList = [];
  List<File> imageFileList = [];

  int selectedGenderPref = AppRes.defaultGenderPref;
  bool showDropdown = false;
  bool isLoading = false;
  ImagePicker imagePicker = ImagePicker();

  void onClipTap(String value) {
    bool selected = selectedList.contains(value);
    if (selected) {
      selectedList.remove(value);
    } else {
      selectedList.add(value);
    }
    notifyListeners();
  }

  void getInterestApiCall() async {
    var interests = await PrefService.getInterest();
    if (interests == null ||
        interests.data == null ||
        interests.data!.isEmpty) {
      // Save a dummy list of interests if not present
      final dummyInterests = [
        Interest(id: 1, title: 'Movies', image: ''),
        Interest(id: 2, title: 'Traveling', image: ''),
        Interest(id: 3, title: 'Cooking', image: ''),
        Interest(id: 4, title: 'Sports', image: ''),
        Interest(id: 5, title: 'Music', image: ''),
      ];
      await PrefService.saveString(
        'interest',
        '{"data":${dummyInterests.map((e) => e.toJson()).toList()}}',
      );
      interests = await PrefService.getInterest();
    }
    if (interests != null && interests.data != null) {
      hobbiesList = interests.data!;
      debugPrint(
        'Loaded hobbiesList: \\${hobbiesList.map((e) => e.title).toList()}',
      );
      notifyListeners();
    }
    getPrefUser();
  }

  void getLocalData() async {
    imageList = userData?.images ?? [];
    fullNameController.text = userData?.fullname ?? '';
    userNameController.text = userData?.username ?? '';
    bioController.text = userData?.bio ?? '';
    aboutController.text = userData?.about ?? '';
    addressController.text = userData?.live ?? '';
    ageController.text = userData?.age?.toString() ?? '';
    selectedGenderPref = userData?.genderPreferred ?? 1;
    currentRangeValues = RangeValues(
      userData?.agePreferredMin?.toDouble() ?? AppRes.ageMin,
      userData?.agePreferredMax?.toDouble() ?? AppRes.ageMax,
    );
    gender =
        userData?.gender == 1
            ? AppRes.male
            : userData?.gender == 2
            ? AppRes.female
            : AppRes.other;
    instagramController.text = userData?.instagram ?? '';
    facebookController.text = userData?.facebook ?? '';
    youtubeController.text = userData?.youtube ?? '';
    notifyListeners();
  }

  void getPrefUser() {
    PrefService.getUserData().then((value) {
      List<String> interestIds = (value?.interests ?? '').split(',');
      selectedList.addAll(interestIds);
      notifyListeners();
    });
  }

  void onImageRemove(int index) {
    File? imageOne;
    for (File image in imageFileList) {
      if (image.path == imageList[index].image) {
        imageOne = image;
      }
    }
    if (imageOne != null) {
      imageFileList.remove(imageOne);
    }
    deleteIds.add(imageList[index].id.toString());
    imageList.removeAt(index);
    notifyListeners();
  }

  void onImageAdd() async {
    selectImages();
  }

  void onBackBtnTap() {
    Get.back();
  }

  void onAllScreenTap() {
    showDropdown = false;
    notifyListeners();
  }

  void onGenderTap() {
    addressFocus.unfocus();
    bioFocus.unfocus();
    aboutFocus.unfocus();
    ageFocus.unfocus();
    showDropdown = !showDropdown;
    notifyListeners();
  }

  void onGenderChange(String value) {
    gender = value;
    showDropdown = false;
    notifyListeners();
  }

  void onSaveTap() {
    Get.back();
    CommonUI.snackBar(message: 'The profile will updated once we integrate backend');
    // debugPrint('Saving userData: \\${userData?.toJson()}');
    // debugPrint('Selected interests: \\${selectedList.toString()}');
    // CommonUI.lottieLoader();
    // // Update userData with new values
    // userData?.fullname = fullNameController.text.trim();
    // userData?.bio = bioController.text.trim();
    // userData?.about = aboutController.text.trim();
    // userData?.live = addressController.text.trim();
    // userData?.age = int.tryParse(ageController.text.trim());
    // userData?.instagram = instagramController.text.trim();
    // userData?.facebook = facebookController.text.trim();
    // userData?.youtube = youtubeController.text.trim();
    // userData?.genderPreferred = selectedGenderPref;
    // userData?.agePreferredMin = currentRangeValues.start.toInt();
    // userData?.agePreferredMax = currentRangeValues.end.toInt();
    // userData?.gender = gender == AppRes.male ? 1 : gender == AppRes.female ? 2 : 3;
    // // Save selected interests as a comma-separated string
    // userData?.interests = selectedList.join(',');
    // // Save images
    // userData?.images = List<Images>.from(imageList);
    // // Save updated user locally
    // PrefService.saveUser(userData);
    // Get.back(result: userData);
  }

  void selectImages() async {
    final selectedImages = await imagePicker.pickMultiImage(
      imageQuality: AppRes.quality,
      maxHeight: AppRes.maxHeight,
      maxWidth: AppRes.maxWidth,
    );
    if (selectedImages.isEmpty) return;
    if (selectedImages.isNotEmpty) {
      for (XFile image in selectedImages) {
        var images = File(image.path);
        imageFileList.add(images);
        imageList.add(
          Images(id: -123, userId: PrefService.userId, image: images.path),
        );
      }
    }
    notifyListeners();
  }

  bool isValid() {
    int i = 0;
    if (imageList.isEmpty) {
      if (imageFileList.isEmpty) {
        CommonUI.snackBarWidget(S.current.pleaseAddAtLeastEtc);
        i++;
      }
      CommonUI.snackBarWidget(S.current.imageIsEmpty);
      i++;
    }
    if (fullNameController.text == '') {
      fullNameError = S.current.enterFullName;
      i++;
    }

    if (aboutController.text == '') {
      aboutFocus.requestFocus();
      CommonUI.snackBarWidget(S.current.enterAbout);
      i++;
    }
    if (ageController.text == '') {
      ageError = S.current.enterAge;
      i++;
    } else if (int.parse(ageController.text) < 18) {
      ageFocus.requestFocus();
      CommonUI.snackBar(message: S.current.youMustBe18);
      return false;
    }
    if (selectedList.isEmpty) {
      CommonUI.snackBarWidget(S.current.pleaseAddAtLeastInterest);
      i++;
    }
    notifyListeners();
    return i == 0 ? true : false;
  }

  @override
  void dispose() {
    bioFocus.dispose();
    aboutFocus.dispose();
    ageFocus.dispose();
    youtubeFocus.dispose();
    addressFocus.dispose();
    fullNameFocus.dispose();
    facebookFocus.dispose();
    instagramFocus.dispose();
    fullNameController.dispose();
    addressController.dispose();
    bioController.dispose();
    aboutController.dispose();
    ageController.dispose();
    instagramController.dispose();
    facebookController.dispose();
    youtubeController.dispose();
    super.dispose();
  }

  void onTextFieldChange(String value) {
    notifyListeners();
  }

  void onGenderSelect(int i) {
    selectedGenderPref = i;
    notifyListeners();
  }

  void onAgeChanged(RangeValues value) {
    currentRangeValues = value;
    notifyListeners();
  }

  static Future<void> ensureDummyInterests() async {
    var interests = await PrefService.getInterest();
    if (interests == null ||
        interests.data == null ||
        interests.data!.isEmpty) {
      final dummyInterests = [
        Interest(id: 1, title: 'Movies', image: ''),
        Interest(id: 2, title: 'Traveling', image: ''),
        Interest(id: 3, title: 'Cooking', image: ''),
        Interest(id: 4, title: 'Sports', image: ''),
        Interest(id: 5, title: 'Music', image: ''),
      ];
      await PrefService.saveString(
        'interest',
        '{"data":${dummyInterests.map((e) => e.toJson()).toList()}}',
      );
    }
  }
}
