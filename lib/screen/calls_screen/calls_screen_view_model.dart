import 'package:get/get.dart';
import 'package:stacked/stacked.dart';
import 'package:orange_ui/screen/call_history_screen/call_history_screen.dart';
import 'package:orange_ui/screen/audio_call_screen/audio_call_screen.dart';
import 'package:orange_ui/screen/video_call_screen/video_call_screen.dart';
import 'package:orange_ui/model/call/call_data.dart';

class CallsScreenViewModel extends BaseViewModel {
  bool isFreePlan = false; // Set to false to allow calls for free users
  List<CallProfile> profiles = [];

  void init() {
    loadProfiles();
  }

  void loadProfiles() {
    // Load dummy profiles for calling
    profiles = [
      
      CallProfile(
        id: '1',
        name: '<PERSON>',
        age: 28,
        imageUrl:
            'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        location: 'Los Angeles, CA',
        isOnline: false,
      ),
      CallProfile(
        id: '2',
        name: '<PERSON>',
        age: 24,
        imageUrl:
            'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
        location: 'Chicago, IL',
        isOnline: true,
      ),
      CallProfile(
        id: '3',
        name: '<PERSON>',
        age: 27,
        imageUrl:
            'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150',
        location: 'Miami, FL',
        isOnline: true,
      ),
      CallProfile(
        id: '4',
        name: 'Maria Garcia',
        age: 26,
        imageUrl:
            'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150',
        location: 'Houston, TX',
        isOnline: false,
      ),
      CallProfile(
        id: '5',
        name: 'Lisa Anderson',
        age: 23,
        imageUrl:
            'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150',
        location: 'Seattle, WA',
        isOnline: true,
      ),
    ];
    notifyListeners();
  }

  void onHistoryTap() {
    // Navigate to call history screen
    Get.to(() => const CallHistoryScreen());
  }

  void onAudioCallTap(CallProfile profile) {
    // Create call data for audio call
    final callData = CallData(
      callId: DateTime.now().millisecondsSinceEpoch.toString(),
      callerId: 'current_user_id',
      callerName: 'Current User',
      callerImage: '',
      receiverId: profile.id,
      receiverName: profile.name,
      receiverImage: profile.imageUrl,
      channelId: DateTime.now().millisecondsSinceEpoch.toString(),
      agoraToken: '',
      callType: 0, // Audio call
      callStatus: 0, // Calling
      startTime: DateTime.now(),
      isIncoming: false,
    );

    Get.to(() => AudioCallScreen(callData: callData));
  }

  void onVideoCallTap(CallProfile profile) {
    // Create call data for video call
    final callData = CallData(
      callId: DateTime.now().millisecondsSinceEpoch.toString(),
      callerId: 'current_user_id',
      callerName: 'Current User',
      callerImage: '',
      receiverId: profile.id,
      receiverName: profile.name,
      receiverImage: profile.imageUrl,
      channelId: DateTime.now().millisecondsSinceEpoch.toString(),
      agoraToken: '',
      callType: 1, // Video call
      callStatus: 0, // Calling
      startTime: DateTime.now(),
      isIncoming: false,
    );

    Get.to(() => VideoCallScreen(callData: callData));
  }

  void onProfileTap(CallProfile profile) {
    // Handle profile tap - could navigate to profile details
    print('Profile tapped: ${profile.name}');
  }

  void onUpgradeTap() {
    // Handle upgrade to premium
    // For now, just show a message or navigate to upgrade screen
    print('Upgrade to premium tapped');
  }
}

class CallProfile {
  final String id;
  final String name;
  final int age;
  final String imageUrl;
  final String location;
  final bool isOnline;

  CallProfile({
    required this.id,
    required this.name,
    required this.age,
    required this.imageUrl,
    required this.location,
    required this.isOnline,
  });
}
