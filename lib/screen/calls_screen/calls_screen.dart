import 'package:flutter/material.dart';
import 'package:orange_ui/screen/calls_screen/calls_screen_view_model.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';
import 'package:stacked/stacked.dart';

class CallsScreen extends StatelessWidget {
  const CallsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<CallsScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder: () => CallsScreenViewModel(),
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: ColorRes.white,
          body: SafeArea(
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Text(
                        'Calls',
                        style: TextStyle(
                          fontSize: 24,
                          fontFamily: FontRes.bold,
                          color: ColorRes.darkBlue,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: model.onHistoryTap,
                        icon: Icon(
                          Icons.history,
                          color: ColorRes.darkBlue,
                          size: 28,
                        ),
                      ),
                    ],
                  ),
                ),

                // Content based on plan type
                Expanded(
                  child:
                      model.isFreePlan
                          ? _buildFreePlanContent(context, model)
                          : _buildProfilesList(context, model),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFreePlanContent(
    BuildContext context,
    CallsScreenViewModel model,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.call_outlined, size: 80, color: ColorRes.grey),
            const SizedBox(height: 24),
            Text(
              'Calls Not Available',
              style: TextStyle(
                fontSize: 20,
                fontFamily: FontRes.bold,
                color: ColorRes.darkBlue,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Audio and video calls are not included in the free plan. Upgrade to premium to unlock this feature.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                fontFamily: FontRes.regular,
                color: ColorRes.grey,
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: model.onUpgradeTap,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorRes.blue,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Text(
                'Upgrade to Premium',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: FontRes.medium,
                  color: ColorRes.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfilesList(BuildContext context, CallsScreenViewModel model) {
    if (model.profiles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 60, color: ColorRes.grey),
            const SizedBox(height: 16),
            Text(
              'No profiles available',
              style: TextStyle(
                fontSize: 18,
                fontFamily: FontRes.medium,
                color: ColorRes.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: model.profiles.length,
      itemBuilder: (context, index) {
        final profile = model.profiles[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: ColorRes.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: ColorRes.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: InkWell(
            onTap: () => model.onProfileTap(profile),
            child: Row(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 25,
                      backgroundImage: NetworkImage(profile.imageUrl),
                    ),
                    if (profile.isOnline)
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 14,
                          height: 14,
                          decoration: BoxDecoration(
                            color: ColorRes.green,
                            shape: BoxShape.circle,
                            border: Border.all(color: ColorRes.white, width: 2),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            profile.name,
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: FontRes.medium,
                              color: ColorRes.darkBlue,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${profile.age}',
                            style: TextStyle(
                              fontSize: 16,
                              fontFamily: FontRes.regular,
                              color: ColorRes.grey,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        profile.location,
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: FontRes.regular,
                          color: ColorRes.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                // Audio call button
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    onPressed: () => model.onAudioCallTap(profile),
                    icon: Icon(Icons.call, color: ColorRes.blue, size: 24),
                    style: IconButton.styleFrom(
                      backgroundColor: ColorRes.blue.withValues(alpha: 0.1),
                      shape: CircleBorder(),
                    ),
                  ),
                ),
                // Video call button
                IconButton(
                  onPressed: () => model.onVideoCallTap(profile),
                  icon: Icon(Icons.videocam, color: ColorRes.blue, size: 24),
                  style: IconButton.styleFrom(
                    backgroundColor: ColorRes.blue.withValues(alpha: 0.1),
                    shape: CircleBorder(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
