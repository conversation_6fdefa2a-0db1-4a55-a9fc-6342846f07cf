import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/screen/subscription_plan_screen/subscription_plan_screen_view_model.dart';
import 'package:orange_ui/screen/subscription_plan_screen/widgets/plan_card.dart';
import 'package:orange_ui/utils/asset_res.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';
import 'package:stacked/stacked.dart';

class SubscriptionPlanScreen extends StatelessWidget {
  const SubscriptionPlanScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<SubscriptionPlanScreenViewModel>.reactive(
      onViewModelReady: (model) => model.init(),
      viewModelBuilder: () => SubscriptionPlanScreenViewModel(),
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: ColorRes.white,
          body: SafeArea(
            child: Column(
              children: [
                // Top Bar
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  child: Row(
                    children: [
                      InkWell(
                        onTap: model.onBackTap,
                        borderRadius: BorderRadius.circular(20),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.arrow_back_ios,
                            color: ColorRes.darkGrey,
                            size: 20,
                          ),
                        ),
                      ),
                      const Expanded(
                        child: Text(
                          'Choose Your Plan',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 18,
                            fontFamily: FontRes.bold,
                            color: ColorRes.black,
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: model.onSkipTap,
                        borderRadius: BorderRadius.circular(20),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          child: const Text(
                            'Skip',
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: FontRes.semiBold,
                              color: ColorRes.grey,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Header Section
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  child: Column(
                    children: [
                      Image.asset(
                        AssetRes.themeLabelWhite,
                        width: Get.width * 0.4,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Unlock Premium Features',
                        style: TextStyle(
                          fontSize: 22,
                          fontFamily: FontRes.bold,
                          color: ColorRes.black,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Choose the perfect plan for your dating journey',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: FontRes.regular,
                          color: ColorRes.grey,
                        ),
                      ),
                    ],
                  ),
                ),

                // Plans List
                Expanded(
                  child:
                      model.isLoading
                          ? Center(child: CommonUI.lottieWidget())
                          : ListView.builder(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            itemCount: model.plans.length,
                            itemBuilder: (context, index) {
                              final plan = model.plans[index];
                              return PlanCard(
                                plan: plan,
                                onTap: () => model.onPlanSelected(plan),
                              );
                            },
                          ),
                ),

                // Bottom Info
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Text(
                        '• Cancel anytime\n• Secure payment\n• No hidden fees',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: FontRes.regular,
                          color: ColorRes.grey,
                          height: 1.5,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          InkWell(
                            onTap: () {
                              // Navigate to Terms of Service
                            },
                            child: const Text(
                              'Terms of Service',
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: FontRes.regular,
                                color: ColorRes.blue,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                          const Text(
                            ' • ',
                            style: TextStyle(
                              fontSize: 12,
                              color: ColorRes.grey,
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              // Navigate to Privacy Policy
                            },
                            child: const Text(
                              'Privacy Policy',
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: FontRes.regular,
                                color: ColorRes.blue,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
