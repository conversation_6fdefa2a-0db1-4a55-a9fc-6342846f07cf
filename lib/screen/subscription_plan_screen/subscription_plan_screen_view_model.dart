import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/model/subscription_plan.dart';
import 'package:orange_ui/screen/dashboard/dashboard_screen.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:stacked/stacked.dart';

class SubscriptionPlanScreenViewModel extends BaseViewModel {
  List<SubscriptionPlan> plans = [];
  List<ProductDetails> products = [];
  late StreamSubscription<dynamic> _subscription;
  final Set<String> _productIds = <String>{};
  bool isLoading = false;

  void init() {
    plans = SubscriptionPlan.getDefaultPlans();
    _initInAppPurchase();
    _loadProducts();
    notifyListeners();
  }

  void _initInAppPurchase() {
    final Stream purchaseUpdated = InAppPurchase.instance.purchaseStream;
    _subscription = purchaseUpdated.listen(
      (purchaseDetailsList) {
        _listenToPurchaseUpdated(purchaseDetailsList);
      },
      onDone: () {
        _subscription.cancel();
      },
      onError: (error) {
        log('Purchase stream error: $error');
      },
    );
  }

  void _loadProducts() async {
    isLoading = true;
    notifyListeners();

    // Add product IDs from plans
    for (var plan in plans) {
      if (plan.productId != null) {
        _productIds.add(plan.productId!);
      }
    }

    if (_productIds.isNotEmpty) {
      final bool available = await InAppPurchase.instance.isAvailable();
      if (available) {
        final ProductDetailsResponse response = await InAppPurchase.instance
            .queryProductDetails(_productIds);

        if (response.notFoundIDs.isNotEmpty) {
          log('Products not found: ${response.notFoundIDs}');
        }

        products = response.productDetails;
        log('Loaded ${products.length} products');
      }
    }

    isLoading = false;
    notifyListeners();
  }

  void _listenToPurchaseUpdated(
    List<PurchaseDetails> purchaseDetailsList,
  ) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        // Show pending UI
        CommonUI.lottieLoader();
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          // Handle error
          Get.back();
          CommonUI.snackBar(
            message: 'Purchase failed: ${purchaseDetails.error?.message}',
          );
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          // Handle successful purchase
          Get.back();
          _handleSuccessfulPurchase(purchaseDetails);
        }

        if (purchaseDetails.pendingCompletePurchase) {
          await InAppPurchase.instance.completePurchase(purchaseDetails);
        }
      }
    }
  }

  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) {
    // Save subscription status to preferences
    PrefService.setSubscriptionStatus(purchaseDetails.productID, true);

    CommonUI.snackBar(message: 'Subscription activated successfully!');

    // Navigate to next screen in the flow
    _navigateToNextScreen();
  }

  void onPlanSelected(SubscriptionPlan plan) {
    if (plan.isFree) {
      // Handle free plan selection
      _navigateToNextScreen();
    } else {
      // Handle paid plan purchase
      _makePurchase(plan);
    }
  }

  void _makePurchase(SubscriptionPlan plan) {
    if (plan.productId == null) {
      CommonUI.snackBar(message: 'Product not available');
      return;
    }

    final ProductDetails? product = products.firstWhereOrNull(
      (p) => p.id == plan.productId,
    );

    if (product == null) {
      CommonUI.snackBar(message: 'Product not found');
      return;
    }

    final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);

    if (Platform.isIOS) {
      InAppPurchase.instance.buyNonConsumable(purchaseParam: purchaseParam);
    } else {
      InAppPurchase.instance.buyNonConsumable(purchaseParam: purchaseParam);
    }
  }

  void _navigateToNextScreen() {
    Get.offAll(() => const DashboardScreen());

    // // Navigate to the next step in registration flow after subscription selection
    // if (userData != null) {
    //   _checkScreenCondition(userData!);
    // } else {
    //   // Fallback navigation
    //   Get.back();
    // }
  }

  // void _checkScreenCondition(RegistrationUserData data) {
  //   if (data.images.isEmpty) {
  //     CommonUI.snackBar(message: 'Select Image');
  //   } else if (data.interests == null || data.interests!.isEmpty) {
  //     CommonUI.snackBar(message: 'Select Hobbies');
  //   } else {
  //     Get.offAll(() => const DashboardScreen());
  //   }
  // }

  void onSkipTap() {
    // Skip subscription selection and continue with free plan
    _navigateToNextScreen();
  }

  void onBackTap() {
    Get.back();
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
