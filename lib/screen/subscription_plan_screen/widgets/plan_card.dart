import 'package:flutter/material.dart';
import 'package:orange_ui/model/subscription_plan.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';

class PlanCard extends StatelessWidget {
  final SubscriptionPlan plan;
  final VoidCallback onTap;

  const PlanCard({super.key, required this.plan, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: plan.isPopular ? ColorRes.darkBlue : ColorRes.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: plan.isPopular ? ColorRes.darkBlue : ColorRes.grey5,
          width: plan.isPopular ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: ColorRes.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Plan Title
            Text(
              plan.title,
              style: TextStyle(
                fontSize: 20,
                fontFamily: FontRes.bold,
                color: plan.isPopular ? ColorRes.white : ColorRes.black,
              ),
            ),

            // Product ID
            if (plan.productId != null) ...[
              const SizedBox(height: 4),
              Text(
                'Product ID: \\${plan.productId}',
                style: TextStyle(
                  fontSize: 12,
                  fontFamily: FontRes.regular,
                  color: plan.isPopular ? ColorRes.white.withOpacity(0.7) : ColorRes.grey,
                ),
              ),
            ],

            // Price (if not free)
            if (!plan.isFree && plan.price != null && plan.duration != null) ...[
              const SizedBox(height: 4),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: plan.price,
                      style: TextStyle(
                        fontSize: 18,
                        fontFamily: FontRes.bold,
                        color:
                            plan.isPopular ? ColorRes.white : ColorRes.darkBlue,
                      ),
                    ),
                    TextSpan(
                      text: '/${plan.duration}',
                      style: TextStyle(
                        fontSize: 14,
                        fontFamily: FontRes.regular,
                        color:
                            plan.isPopular
                                ? ColorRes.white.withOpacity(0.8)
                                : ColorRes.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Duration (if not free and not already shown)
            if (!plan.isFree && plan.duration != null) ...[
              const SizedBox(height: 2),
              Text(
                'Duration: \\${plan.duration}',
                style: TextStyle(
                  fontSize: 12,
                  fontFamily: FontRes.regular,
                  color: plan.isPopular ? ColorRes.white.withOpacity(0.7) : ColorRes.grey,
                ),
              ),
            ],

            // Is Popular / Is Free
            const SizedBox(height: 4),
            Row(
              children: [
                if (plan.isPopular)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: ColorRes.darkBlue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Most Popular',
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: FontRes.semiBold,
                        color: ColorRes.darkBlue,
                      ),
                    ),
                  ),
                if (plan.isFree)
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: ColorRes.green,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Free',
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: FontRes.semiBold,
                        color: ColorRes.white,
                      ),
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 16),

            // Features List
            ...plan.features.map(
              (feature) => Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 4, right: 10),
                      child: Icon(
                        feature.startsWith('×') ? Icons.close : Icons.check,
                        size: 16,
                        color:
                            feature.startsWith('×')
                                ? ColorRes.red
                                : (plan.isPopular
                                    ? ColorRes.white
                                    : ColorRes.lightGreen),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        feature.startsWith('×')
                            ? feature.substring(2)
                            : feature,
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: FontRes.regular,
                          color:
                              plan.isPopular
                                  ? ColorRes.white.withValues(alpha: 0.9)
                                  : ColorRes.darkGrey,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Buy Now Button
            InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(25),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color:
                      plan.isFree
                          ? ColorRes.blue
                          : (plan.isPopular ? ColorRes.white : ColorRes.blue),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Text(
                    plan.isFree ? 'Continue Free' : 'Buy Now',
                    style: TextStyle(
                      fontSize: 16,
                      fontFamily: FontRes.semiBold,
                      color:
                          plan.isPopular && !plan.isFree
                              ? ColorRes.darkBlue
                              : ColorRes.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
