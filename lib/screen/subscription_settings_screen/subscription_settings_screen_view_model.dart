import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:stacked/stacked.dart';

class SubscriptionSettingsScreenViewModel extends BaseViewModel {
  bool isPremium = false;
  String currentPlan = 'Free Plan';
  String planDescription = 'Basic features with limited access';
  String nextBillingDate = '';

  List<Map<String, dynamic>> availablePlans = [
    {
      'name': 'Free Plan',
      'description': 'Basic features with limited access',
      'price': 'Free',
      'period': '',
    },
    {
      'name': 'Gold Plan',
      'description': 'Unlimited messaging, 40 likes per day, 5 super likes per week',
      'price': '\$24.99',
      'period': '/month',
    },
    {
      'name': 'Diamond Plan',
      'description': 'All Gold features plus unlimited calls and premium support',
      'price': '\$49.99',
      'period': '/month',
    },
  ];

  void init() {
    loadSubscriptionData();
  }

  void loadSubscriptionData() {
    // Load user subscription data from preferences or API
    // For now, using dummy data based on user preferences
    PrefService.getUserData().then((userData) {
      if (userData != null) {
        // Check if user has premium subscription
        // This would typically come from your backend/subscription service
        isPremium = false; // Default to free for demo
        
        if (isPremium) {
          currentPlan = 'Gold Plan';
          planDescription = 'Unlimited messaging, 40 likes per day, 5 super likes per week';
          nextBillingDate = 'Dec 15, 2024'; // This would come from subscription data
        } else {
          currentPlan = 'Free Plan';
          planDescription = 'Basic features with limited access';
          nextBillingDate = '';
        }
        
        notifyListeners();
      }
    });
  }

  void onManageSubscriptionTap() {
    if (isPremium) {
      // Navigate to subscription management (cancel, change plan, etc.)
      _showManageSubscriptionOptions();
    } else {
      // Navigate to upgrade/purchase flow
      _showUpgradeOptions();
    }
  }

  void _showManageSubscriptionOptions() {
    // Show options to manage current subscription
    CommonUI.snackBarWidget('Manage subscription functionality - would integrate with app store/play store');
  }

  void _showUpgradeOptions() {
    // Show upgrade options and payment flow
    CommonUI.snackBarWidget('Upgrade functionality - would integrate with payment gateway');
  }

  void onPlanTap(Map<String, dynamic> plan) {
    if (plan['name'] == currentPlan) {
      return; // Already on this plan
    }
    
    if (plan['name'] == 'Free Plan') {
      _downgradeToPlan(plan);
    } else {
      _upgradeToPlan(plan);
    }
  }

  void _upgradeToPlan(Map<String, dynamic> plan) {
    // Handle upgrade to premium plan
    CommonUI.snackBarWidget('Upgrading to ${plan['name']} - would integrate with payment gateway');
  }

  void _downgradeToPlan(Map<String, dynamic> plan) {
    // Handle downgrade to free plan
    CommonUI.snackBarWidget('Downgrading to ${plan['name']} - would process cancellation');
  }
}
