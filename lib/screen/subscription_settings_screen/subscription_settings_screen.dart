import 'package:flutter/material.dart';
import 'package:orange_ui/common/top_bar_area.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/model/subscription_plan.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';
import 'package:orange_ui/utils/asset_res.dart';
import 'package:stacked/stacked.dart';
import 'subscription_settings_screen_view_model.dart';

class SubscriptionSettingsScreen extends StatelessWidget {
  const SubscriptionSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<SubscriptionSettingsScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder: () => SubscriptionSettingsScreenViewModel(),
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: ColorRes.white,
          body: <PERSON><PERSON><PERSON>(
            child: Column(
              children: [
                TopBarArea(title2: S.current.subscriptionSettings),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: ListView(
                      children: [
                        const SizedBox(height: 20),
                        _buildCurrentPlanCard(model),
                        const SizedBox(height: 24),
                        _buildSubscriptionOptions(model),
                        const Spacer(),
                        _buildManageSubscriptionButton(model),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCurrentPlanCard(SubscriptionSettingsScreenViewModel model) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [ColorRes.blue, ColorRes.darkBlue],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image.asset(AssetRes.diamond, height: 24, width: 24),
              const SizedBox(width: 8),
              Text(
                model.currentPlan,
                style: const TextStyle(
                  color: ColorRes.white,
                  fontSize: 18,
                  fontFamily: FontRes.bold,
                ),
              ),
              const Spacer(),
              if (model.isPremium)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: ColorRes.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'ACTIVE',
                    style: TextStyle(
                      color: ColorRes.white,
                      fontSize: 10,
                      fontFamily: FontRes.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            model.planDescription,
            style: const TextStyle(
              color: ColorRes.white,
              fontSize: 14,
              fontFamily: FontRes.regular,
            ),
          ),
          if (model.isPremium) ...[
            const SizedBox(height: 12),
            Text(
              'Next billing: ${model.nextBillingDate}',
              style: TextStyle(
                color: ColorRes.white.withValues(alpha: 0.8),
                fontSize: 12,
                fontFamily: FontRes.regular,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSubscriptionOptions(SubscriptionSettingsScreenViewModel model) {
    // Use SubscriptionPlan model for detailed plan info
    final plans = SubscriptionPlan.getDefaultPlans();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Plans',
          style: const TextStyle(
            color: ColorRes.darkBlue,
            fontSize: 16,
            fontFamily: FontRes.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...plans.map((plan) => _buildPlanOptionFromModel(plan, model)),
      ],
    );
  }

  Widget _buildPlanOptionFromModel(
    SubscriptionPlan plan,
    SubscriptionSettingsScreenViewModel model,
  ) {
    final isCurrentPlan = plan.title == model.currentPlan;
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: ColorRes.grey10,
        border:
            isCurrentPlan ? Border.all(color: ColorRes.blue, width: 2) : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (plan.isPopular)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: ColorRes.darkBlue,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Most Popular',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: FontRes.semiBold,
                      color: ColorRes.darkBlue,
                    ),
                  ),
                ),
              if (plan.isFree)
                Container(
                  margin: const EdgeInsets.only(left: 8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: ColorRes.green,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Free',
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: FontRes.semiBold,
                      color: ColorRes.white,
                    ),
                  ),
                ),
              const Spacer(),
              if (plan.productId != null)
                Text(
                  'ID: ${plan.productId}',
                  style: TextStyle(fontSize: 10, color: ColorRes.grey),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            plan.title,
            style: const TextStyle(
              color: ColorRes.darkBlue,
              fontSize: 16,
              fontFamily: FontRes.semiBold,
            ),
          ),
          if (plan.price != null && plan.duration != null)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                '${plan.price} / ${plan.duration}',
                style: const TextStyle(
                  color: ColorRes.blue,
                  fontSize: 15,
                  fontFamily: FontRes.bold,
                ),
              ),
            ),
          const SizedBox(height: 8),
          ...plan.features.map(
            (feature) => Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  feature.startsWith('×') ? Icons.close : Icons.check,
                  size: 16,
                  color:
                      feature.startsWith('×')
                          ? ColorRes.red
                          : ColorRes.lightGreen,
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    feature.startsWith('×') ? feature.substring(2) : feature,
                    style: const TextStyle(
                      color: ColorRes.darkGrey,
                      fontSize: 14,
                      fontFamily: FontRes.regular,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManageSubscriptionButton(
    SubscriptionSettingsScreenViewModel model,
  ) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: model.onManageSubscriptionTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorRes.blue,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: Text(
          model.isPremium ? 'Manage Subscription' : 'Upgrade to Premium',
          style: const TextStyle(
            color: ColorRes.white,
            fontSize: 16,
            fontFamily: FontRes.semiBold,
          ),
        ),
      ),
    );
  }
}
