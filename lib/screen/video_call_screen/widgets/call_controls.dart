import 'package:flutter/material.dart';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/utils/color_res.dart';

class CallControls extends StatelessWidget {
  final bool isMuted;
  final bool isVideoEnabled;
  final bool isSpeakerOn;
  final VoidCallback onMuteTap;
  final VoidCallback onVideoTap;
  final VoidCallback onSpeakerTap;
  final VoidCallback onEndCallTap;
  final bool isIncoming;
  final VoidCallback? onAcceptTap;
  final VoidCallback? onRejectTap;
  final int callStatus;

  const CallControls({
    super.key,
    required this.isMuted,
    required this.isVideoEnabled,
    required this.isSpeakerOn,
    required this.onMuteTap,
    required this.onVideoTap,
    required this.onSpeakerTap,
    required this.onEndCallTap,
    required this.isIncoming,
    this.onAcceptTap,
    this.onRejectTap,
    required this.callStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            ColorRes.transparent,
            ColorRes.black.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isIncoming && callStatus == CallStatus.calling)
              _buildIncomingCallControls()
            else
              _buildActiveCallControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildIncomingCallControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Reject call
        _buildControlButton(
          icon: Icons.call_end,
          color: ColorRes.red,
          onTap: onRejectTap ?? () {},
          size: 60,
        ),
        // Accept call
        _buildControlButton(
          icon: Icons.call,
          color: ColorRes.green,
          onTap: onAcceptTap ?? () {},
          size: 60,
        ),
      ],
    );
  }

  Widget _buildActiveCallControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Mute/Unmute
        _buildControlButton(
          icon: isMuted ? Icons.mic_off : Icons.mic,
          color: isMuted ? ColorRes.red : ColorRes.white,
          backgroundColor: isMuted ? ColorRes.white : ColorRes.grey.withValues(alpha: 0.3),
          onTap: onMuteTap,
        ),
        
        // Video on/off
        _buildControlButton(
          icon: isVideoEnabled ? Icons.videocam : Icons.videocam_off,
          color: isVideoEnabled ? ColorRes.white : ColorRes.red,
          backgroundColor: isVideoEnabled ? ColorRes.grey.withValues(alpha: 0.3) : ColorRes.white,
          onTap: onVideoTap,
        ),
        
        // Speaker on/off
        _buildControlButton(
          icon: isSpeakerOn ? Icons.volume_up : Icons.volume_down,
          color: isSpeakerOn ? ColorRes.white : ColorRes.grey,
          backgroundColor: ColorRes.grey.withValues(alpha: 0.3),
          onTap: onSpeakerTap,
        ),
        
        // End call
        _buildControlButton(
          icon: Icons.call_end,
          color: ColorRes.white,
          backgroundColor: ColorRes.red,
          onTap: onEndCallTap,
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    Color? backgroundColor,
    required VoidCallback onTap,
    double size = 50,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: backgroundColor ?? ColorRes.grey.withValues(alpha: 0.3),
          shape: BoxShape.circle,
          border: Border.all(
            color: ColorRes.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: color,
          size: size * 0.5,
        ),
      ),
    );
  }
}
