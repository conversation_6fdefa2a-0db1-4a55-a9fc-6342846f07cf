import 'package:flutter/material.dart';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';

class CallInfoOverlay extends StatelessWidget {
  final CallData callData;
  final String callDuration;
  final bool isConnected;
  final VoidCallback onSwitchCamera;

  const CallInfoOverlay({
    super.key,
    required this.callData,
    required this.callDuration,
    required this.isConnected,
    required this.onSwitchCamera,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.only(top: 50, left: 20, right: 20, bottom: 20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              ColorRes.black.withValues(alpha: 0.8),
              ColorRes.transparent,
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Call info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          callData.isIncoming == true
                              ? callData.callerName ?? 'Unknown'
                              : callData.receiverName ?? 'Unknown',
                          style: const TextStyle(
                            color: ColorRes.white,
                            fontSize: 20,
                            fontFamily: FontRes.medium,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          isConnected ? callDuration : _getCallStatusText(),
                          style: const TextStyle(
                            color: ColorRes.white,
                            fontSize: 14,
                            fontFamily: FontRes.regular,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Switch camera button
                  GestureDetector(
                    onTap: onSwitchCamera,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: ColorRes.grey.withValues(alpha: 0.3),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: ColorRes.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.switch_camera,
                        color: ColorRes.white,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getCallStatusText() {
    switch (callData.callStatus) {
      case CallStatus.calling:
        return callData.isIncoming == true ? 'Incoming call...' : 'Calling...';
      case CallStatus.answered:
        return 'Connected';
      case CallStatus.ended:
        return 'Call ended';
      case CallStatus.missed:
        return 'Missed call';
      case CallStatus.rejected:
        return 'Call rejected';
      default:
        return 'Connecting...';
    }
  }
}
