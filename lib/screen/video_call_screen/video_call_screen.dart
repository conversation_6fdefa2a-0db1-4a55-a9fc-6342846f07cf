import 'package:flutter/material.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/model/setting.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/video_call_screen/video_call_screen_view_model.dart';
import 'package:orange_ui/screen/video_call_screen/widgets/call_controls.dart';
import 'package:orange_ui/screen/video_call_screen/widgets/call_info_overlay.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:stacked/stacked.dart';

class VideoCallScreen extends StatelessWidget {
  final CallData callData;
  final RegistrationUserData? userData;
  final Appdata? settingData;

  const VideoCallScreen({
    super.key,
    required this.callData,
    this.userData,
    this.settingData,
  });

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<VideoCallScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder:
          () => VideoCallScreenViewModel(
            callData: callData,
            userData: userData,
            settingData: settingData,
          ),
      builder: (context, model, child) {
        return PopScope(
          canPop: false,
          child: Scaffold(
            backgroundColor: ColorRes.black,
            body: Stack(
              children: [
                // Remote video (full screen)
                model.isLoading ? CommonUI.lottieWidget() : model.remoteVideo(),

                // Local video (small overlay)
                if (model.localUserJoined && !model.isLoading)
                  Positioned(
                    top: 60,
                    right: 20,
                    child: Container(
                      width: 120,
                      height: 160,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: ColorRes.white, width: 2),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: model.localVideo(),
                      ),
                    ),
                  ),

                // Call info overlay (top)
                CallInfoOverlay(
                  callData: model.callData,
                  callDuration: model.callDuration,
                  isConnected: model.isConnected,
                  onSwitchCamera: model.onSwitchCamera,
                ),

                // Call controls (bottom)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: CallControls(
                    isMuted: model.isMuted,
                    isVideoEnabled: model.isVideoEnabled,
                    isSpeakerOn: model.isSpeakerOn,
                    onMuteTap: model.onMuteTap,
                    onVideoTap: model.onVideoTap,
                    onSpeakerTap: model.onSpeakerTap,
                    onEndCallTap: model.onEndCallTap,
                    isIncoming: model.callData.isIncoming ?? false,
                    onAcceptTap: model.onAcceptCallTap,
                    onRejectTap: model.onRejectCallTap,
                    callStatus: model.callData.callStatus ?? CallStatus.calling,
                  ),
                ),

                // Loading overlay
                if (model.isLoading)
                  Container(
                    color: ColorRes.black.withValues(alpha: 0.7),
                    child: const Center(
                      child: CircularProgressIndicator(color: ColorRes.white),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
