import 'package:flutter/material.dart';
import 'package:orange_ui/common/dashboard_top_bar.dart';
import 'package:orange_ui/screen/home_screen/home_screen_view_model.dart';
import 'package:orange_ui/screen/home_screen/widgets/profile_card.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';
import 'package:stacked/stacked.dart';
import 'package:confetti/confetti.dart';
import 'dart:math';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late ConfettiController _confettiController;

  @override
  void initState() {
    super.initState();
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 1));
  }

  @override
  void dispose() {
    _confettiController.dispose();
    super.dispose();
  }

  void _onSuperlike() {
    _confettiController.play();
  }

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<HomeScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder: () => HomeScreenViewModel(),
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: ColorRes.white,
          body: SafeArea(
            child: Stack(
              children: [
                Column(
                  children: [
                    // Header
                    DashboardTopBar(onNotificationTap: model.onNotificationTap, isDating: model.settingAppData?.isDating,
                    ),
                    // Profile Cards Stack
                    Expanded(
                      child: model.isLoading
                          ? Center(
                              child: CircularProgressIndicator(
                                color: ColorRes.blue,
                              ),
                            )
                          : model.profiles.isEmpty
                              ? _buildEmptyState()
                              : Stack(
                                  children: [
                                    // Profile cards
                                    for (int i = model.profiles.length - 1;
                                        i >= 0;
                                        i--)
                                      if (i >= model.currentIndex)
                                        ProfileCard(
                                          profile: model.profiles[i],
                                          isTop: i == model.currentIndex,
                                          onSwipeLeft: () =>
                                              model.onSwipeLeft(i),
                                          onSwipeRight: () =>
                                              model.onSwipeRight(i),
                                          onTap: () =>
                                              model.onProfileTap(model.profiles[i]),
                                        ),
                                  ],
                                ),
                    ),

                    // Action Buttons
                    if (model.profiles.isNotEmpty &&
                        model.currentIndex < model.profiles.length)
                      Container(
                        padding: const EdgeInsets.all(24),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // Pass button
                            GestureDetector(
                              onTap: () => model.onSwipeLeft(model.currentIndex),
                              child: Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: ColorRes.white,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: ColorRes.grey, width: 2),
                                  boxShadow: [
                                    BoxShadow(
                                      color: ColorRes.grey.withOpacity(0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.close,
                                  color: Colors.red,
                                  size: 30,
                                ),
                              ),
                            ),

                            // Like button
                            GestureDetector(
                              onTap: () => model.onSwipeRight(model.currentIndex),
                              child: Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: ColorRes.blue,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: ColorRes.blue.withOpacity(0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.favorite,
                                  color: ColorRes.white,
                                  size: 30,
                                ),
                              ),
                            ),

                            // Superlike button
                            GestureDetector(
                              onTap: () {
                                model.onSwipeSuperlike(model.currentIndex);
                                _onSuperlike();
                              },
                              child: Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: Colors.yellow,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.yellow.withOpacity(0.6),
                                      blurRadius: 12,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.bolt,
                                  color: Colors.white,
                                  size: 30,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                Align(
                  alignment: Alignment.topCenter,
                  child: ConfettiWidget(
                    confettiController: _confettiController,
                    blastDirectionality: BlastDirectionality.explosive,
                    shouldLoop: false,
                    colors: const [
                      Colors.yellow,
                      Colors.orange,
                      Colors.red,
                      Colors.blue,
                      Colors.green,
                      Colors.purple,
                    ],
                    createParticlePath: drawStar,
                    emissionFrequency: 0.05,
                    numberOfParticles: 20,
                    gravity: 0.1,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_outline,
            size: 80,
            color: ColorRes.grey,
          ),
          const SizedBox(height: 24),
          Text(
            'No More Profiles',
            style: TextStyle(
              fontSize: 20,
              fontFamily: FontRes.bold,
              color: ColorRes.darkBlue,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Check back later for new matches!',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontFamily: FontRes.regular,
              color: ColorRes.grey,
            ),
          ),
        ],
      ),
    );
  }
}

Path drawStar(Size size) {
  // Method to draw a star shape for confetti particles
  final Path path = Path();
  final double halfWidth = size.width / 2;
  final double halfHeight = size.height / 2;
  path.moveTo(halfWidth, 0);
  path.lineTo(halfWidth * 0.6, halfHeight * 0.8);
  path.lineTo(0, halfHeight * 0.8);
  path.lineTo(halfWidth * 0.5, halfHeight);
  path.lineTo(0, size.height);
  path.lineTo(halfWidth, halfHeight * 1.2);
  path.lineTo(size.width, size.height);
  path.lineTo(halfWidth * 1.5, halfHeight);
  path.lineTo(size.width, halfHeight * 0.8);
  path.lineTo(halfWidth * 1.4, halfHeight * 0.8);
  path.close();
  return path;
}
