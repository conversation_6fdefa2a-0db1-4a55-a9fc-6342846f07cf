import 'package:flutter/material.dart';
import 'package:orange_ui/model/search/search_filter.dart';
import 'package:orange_ui/service/filter_service.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';

class FilterBottomSheet extends StatefulWidget {
  final Function(SearchFilter) onFiltersApplied;

  const FilterBottomSheet({
    super.key,
    required this.onFiltersApplied,
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late SearchFilter _tempFilter;
  final FilterService _filterService = FilterService.instance;

  @override
  void initState() {
    super.initState();
    _tempFilter = SearchFilter(
      genderPreference: _filterService.currentFilter.genderPreference,
      distanceKm: _filterService.currentFilter.distanceKm,
      distanceUnit: _filterService.currentFilter.distanceUnit,
      selectedRegion: _filterService.currentFilter.selectedRegion,
      selectedCountry: _filterService.currentFilter.selectedCountry,
      ageRange: _filterService.currentFilter.ageRange,
      selectedInterests: List.from(_filterService.currentFilter.selectedInterests),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: ColorRes.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: ColorRes.grey.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filters',
                  style: TextStyle(
                    fontSize: 24,
                    fontFamily: FontRes.bold,
                    color: ColorRes.darkBlue,
                  ),
                ),
                TextButton(
                  onPressed: _resetFilters,
                  child: Text(
                    'Reset',
                    style: TextStyle(
                      fontSize: 16,
                      fontFamily: FontRes.medium,
                      color: ColorRes.blue,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildGenderPreference(),
                  const SizedBox(height: 24),
                  _buildDistanceFilter(),
                  const SizedBox(height: 24),
                  _buildRegionFilter(),
                  const SizedBox(height: 24),
                  _buildAgeRangeFilter(),
                  const SizedBox(height: 24),
                  _buildInterestsFilter(),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
          
          // Apply button
          Container(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _applyFilters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorRes.blue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: Text(
                  'Apply Filters',
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: FontRes.bold,
                    color: ColorRes.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenderPreference() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Gender Preference',
          style: TextStyle(
            fontSize: 18,
            fontFamily: FontRes.bold,
            color: ColorRes.darkBlue,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: ['Male', 'Female', 'Both'].map((gender) {
            bool isSelected = _tempFilter.genderPreference == gender;
            return Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _tempFilter = _tempFilter.copyWith(genderPreference: gender);
                  });
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: isSelected ? ColorRes.blue : ColorRes.white,
                    border: Border.all(
                      color: isSelected ? ColorRes.blue : ColorRes.grey,
                    ),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Text(
                    gender,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontFamily: FontRes.medium,
                      color: isSelected ? ColorRes.white : ColorRes.darkBlue,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDistanceFilter() {
    double displayDistance = _tempFilter.distanceUnit == 'miles' 
        ? _tempFilter.distanceKm / 1.60934 
        : _tempFilter.distanceKm;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Distance',
              style: TextStyle(
                fontSize: 18,
                fontFamily: FontRes.bold,
                color: ColorRes.darkBlue,
              ),
            ),
            Row(
              children: ['km', 'miles'].map((unit) {
                bool isSelected = _tempFilter.distanceUnit == unit;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _tempFilter = _tempFilter.copyWith(distanceUnit: unit);
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isSelected ? ColorRes.blue : ColorRes.white,
                      border: Border.all(
                        color: isSelected ? ColorRes.blue : ColorRes.grey,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      unit,
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: FontRes.medium,
                        color: isSelected ? ColorRes.white : ColorRes.darkBlue,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Slider(
          value: displayDistance,
          min: 1,
          max: _tempFilter.distanceUnit == 'miles' ? 100 : 160,
          divisions: _tempFilter.distanceUnit == 'miles' ? 99 : 159,
          activeColor: ColorRes.blue,
          inactiveColor: ColorRes.grey.withOpacity(0.3),
          onChanged: (value) {
            setState(() {
              double kmValue = _tempFilter.distanceUnit == 'miles' 
                  ? value * 1.60934 
                  : value;
              _tempFilter = _tempFilter.copyWith(distanceKm: kmValue);
            });
          },
        ),
        Text(
          '${displayDistance.round()} ${_tempFilter.distanceUnit}',
          style: TextStyle(
            fontSize: 14,
            fontFamily: FontRes.medium,
            color: ColorRes.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildRegionFilter() {
    List<RegionData> regions = _filterService.getRegionsData();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Region & Country',
          style: TextStyle(
            fontSize: 18,
            fontFamily: FontRes.bold,
            color: ColorRes.darkBlue,
          ),
        ),
        const SizedBox(height: 12),
        
        // Region dropdown
        DropdownButtonFormField<String>(
          value: _tempFilter.selectedRegion,
          decoration: InputDecoration(
            hintText: 'Select Region',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: ColorRes.grey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: BorderSide(color: ColorRes.blue),
            ),
          ),
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('Any Region'),
            ),
            ...regions.map((region) => DropdownMenuItem<String>(
              value: region.name,
              child: Text(region.name),
            )),
          ],
          onChanged: (value) {
            setState(() {
              _tempFilter = _tempFilter.copyWith(
                selectedRegion: value,
                selectedCountry: null, // Reset country when region changes
              );
            });
          },
        ),
        
        const SizedBox(height: 12),
        
        // Country dropdown (only show if region is selected)
        if (_tempFilter.selectedRegion != null) ...[
          DropdownButtonFormField<String>(
            value: _tempFilter.selectedCountry,
            decoration: InputDecoration(
              hintText: 'Select Country',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: ColorRes.grey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide(color: ColorRes.blue),
              ),
            ),
            items: [
              const DropdownMenuItem<String>(
                value: null,
                child: Text('Any Country'),
              ),
              ...regions
                  .firstWhere((r) => r.name == _tempFilter.selectedRegion!)
                  .countries
                  .map((country) => DropdownMenuItem<String>(
                        value: country,
                        child: Text(country),
                      )),
            ],
            onChanged: (value) {
              setState(() {
                _tempFilter = _tempFilter.copyWith(selectedCountry: value);
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildAgeRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Age Range',
          style: TextStyle(
            fontSize: 18,
            fontFamily: FontRes.bold,
            color: ColorRes.darkBlue,
          ),
        ),
        const SizedBox(height: 12),
        RangeSlider(
          values: _tempFilter.ageRange,
          min: 18,
          max: 65,
          divisions: 47,
          activeColor: ColorRes.blue,
          inactiveColor: ColorRes.grey.withOpacity(0.3),
          labels: RangeLabels(
            _tempFilter.ageRange.start.round().toString(),
            _tempFilter.ageRange.end.round().toString(),
          ),
          onChanged: (values) {
            setState(() {
              _tempFilter = _tempFilter.copyWith(ageRange: values);
            });
          },
        ),
        Text(
          '${_tempFilter.ageRange.start.round()} - ${_tempFilter.ageRange.end.round()} years',
          style: TextStyle(
            fontSize: 14,
            fontFamily: FontRes.medium,
            color: ColorRes.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildInterestsFilter() {
    List<String> availableInterests = _filterService.getAvailableInterests();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Interests (Optional)',
          style: TextStyle(
            fontSize: 18,
            fontFamily: FontRes.bold,
            color: ColorRes.darkBlue,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: availableInterests.map((interest) {
            bool isSelected = _tempFilter.selectedInterests.contains(interest);
            return GestureDetector(
              onTap: () {
                setState(() {
                  List<String> newInterests = List.from(_tempFilter.selectedInterests);
                  if (isSelected) {
                    newInterests.remove(interest);
                  } else {
                    newInterests.add(interest);
                  }
                  _tempFilter = _tempFilter.copyWith(selectedInterests: newInterests);
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? ColorRes.blue : ColorRes.white,
                  border: Border.all(
                    color: isSelected ? ColorRes.blue : ColorRes.grey,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  interest,
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: FontRes.medium,
                    color: isSelected ? ColorRes.white : ColorRes.darkBlue,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _resetFilters() {
    setState(() {
      _tempFilter = SearchFilter();
    });
  }

  void _applyFilters() {
    _filterService.saveFilters(_tempFilter);
    widget.onFiltersApplied(_tempFilter);
    Navigator.pop(context);
  }
}
