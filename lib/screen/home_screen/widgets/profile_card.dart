import 'package:flutter/material.dart';
import 'package:orange_ui/screen/home_screen/home_screen_view_model.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';

class ProfileCard extends StatefulWidget {
  final DatingProfile profile;
  final bool isTop;
  final VoidCallback onSwipeLeft;
  final VoidCallback onSwipeRight;
  final VoidCallback onTap;

  const ProfileCard({
    super.key,
    required this.profile,
    required this.isTop,
    required this.onSwipeLeft,
    required this.onSwipeRight,
    required this.onTap,
  });

  @override
  State<ProfileCard> createState() => _ProfileCardState();
}

class _ProfileCardState extends State<ProfileCard> {
  int currentImageIndex = 0;
  PageController pageController = PageController();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        margin: EdgeInsets.all(widget.isTop ? 16 : 20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: ColorRes.grey.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Image carousel
              PageView.builder(
                controller: pageController,
                onPageChanged: (index) {
                  setState(() {
                    currentImageIndex = index;
                  });
                },
                itemCount: widget.profile.imageUrls.length,
                itemBuilder: (context, index) {
                  return Image.network(
                    widget.profile.imageUrls[index],
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: ColorRes.grey.withOpacity(0.3),
                        child: Icon(
                          Icons.person,
                          size: 100,
                          color: ColorRes.grey,
                        ),
                      );
                    },
                  );
                },
              ),
              
              // Image indicators
              if (widget.profile.imageUrls.length > 1)
                Positioned(
                  top: 16,
                  left: 16,
                  right: 16,
                  child: Row(
                    children: List.generate(
                      widget.profile.imageUrls.length,
                      (index) => Expanded(
                        child: Container(
                          height: 3,
                          margin: EdgeInsets.symmetric(horizontal: 2),
                          decoration: BoxDecoration(
                            color: index == currentImageIndex
                                ? ColorRes.white
                                : ColorRes.white.withOpacity(0.4),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              
              // Gradient overlay
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.8),
                    ],
                  ),
                ),
              ),
              
              // Profile information
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Name and age
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              '${widget.profile.name}, ${widget.profile.age}',
                              style: TextStyle(
                                fontSize: 24,
                                fontFamily: FontRes.bold,
                                color: ColorRes.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Location and distance
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 16,
                            color: ColorRes.white.withOpacity(0.8),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            widget.profile.distance,
                            style: TextStyle(
                              fontSize: 14,
                              fontFamily: FontRes.regular,
                              color: ColorRes.white.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Bio
                      Text(
                        widget.profile.bio,
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: FontRes.regular,
                          color: ColorRes.white,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Interests
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: widget.profile.interests.take(3).map((interest) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: ColorRes.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: ColorRes.white.withOpacity(0.3),
                              ),
                            ),
                            child: Text(
                              interest,
                              style: TextStyle(
                                fontSize: 12,
                                fontFamily: FontRes.medium,
                                color: ColorRes.white,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Tap areas for image navigation
              if (widget.profile.imageUrls.length > 1) ...[
                // Left tap area
                Positioned(
                  left: 0,
                  top: 0,
                  bottom: 100,
                  width: MediaQuery.of(context).size.width * 0.3,
                  child: GestureDetector(
                    onTap: () {
                      if (currentImageIndex > 0) {
                        pageController.previousPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }
                    },
                  ),
                ),
                
                // Right tap area
                Positioned(
                  right: 0,
                  top: 0,
                  bottom: 100,
                  width: MediaQuery.of(context).size.width * 0.3,
                  child: GestureDetector(
                    onTap: () {
                      if (currentImageIndex < widget.profile.imageUrls.length - 1) {
                        pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }
}
