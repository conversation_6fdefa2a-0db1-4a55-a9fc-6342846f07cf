import 'package:flutter/material.dart';
import 'package:orange_ui/screen/dashboard/dashboard_screen_view_model.dart';
import 'package:orange_ui/screen/dashboard/widgets/bottom_bar.dart';
import 'package:orange_ui/screen/home_screen/home_screen.dart';
import 'package:orange_ui/screen/message_screen/message_screen.dart';
import 'package:orange_ui/screen/calls_screen/calls_screen.dart';
import 'package:orange_ui/screen/save_screen/save_screen.dart';
import 'package:orange_ui/screen/profile_screen/profile_screen.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:stacked/stacked.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<DashboardScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder: () => DashboardScreenViewModel(),
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: ColorRes.white,
          bottomNavigationBar: BottomBar(
            pageIndex: model.pageIndex,
            onBottomBarTap: model.onBottomBarTap,
            settingAppData: model.settingAppData,
          ),
          body: SafeArea(
            bottom: false,
            child:
                model.pageIndex == 0
                    ? const HomeScreen() // Home
                    : model.pageIndex == 1
                    ? const MessageScreen() // Messages
                    : model.pageIndex == 2
                    ? const CallsScreen() // Calls
                    : model.pageIndex == 3
                    ? const SaveScreen() // Save
                    : const ProfileScreen(), // Profile
          ),
        );
      },
    );
  }
}
