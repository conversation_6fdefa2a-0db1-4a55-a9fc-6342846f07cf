import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:orange_ui/api_provider/api_provider.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/dashboard/dashboard_screen.dart';
import 'package:orange_ui/screen/login_pwd_screen/login_pwd_screen.dart';

import 'package:orange_ui/screen/forgot_password_screen/forgot_password_screen.dart';
import 'package:orange_ui/screen/register_screen/register_screen.dart';
import 'package:orange_ui/screen/starting_profile_screen/starting_profile_screen.dart';
import 'package:orange_ui/service/firebase_notification_manager.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:stacked/stacked.dart';

class LoginDashboardScreenViewModel extends BaseViewModel {
  String? tokenId;
  TextEditingController emailController = TextEditingController();
  FocusNode emailFocus = FocusNode();
  String emailError = "";
  String appleUserName = '';
  FocusNode resetFocusNode = FocusNode();

  void init() {
    FirebaseNotificationManager.shared.getNotificationToken(
      (token) => tokenId = token,
    );
    getEmail();
  }

  void getEmail() async {
    emailController.text = await PrefService.getEmail() ?? '';
  }

  bool isValid() {
    if (emailController.text == "") {
      emailError = S.current.enterEmail;
      return false;
    } else {
      emailError = "";
      return true;
    }
  }

  void onContinueTap() {
    bool validation = isValid();
    notifyListeners();
    emailFocus.unfocus();
    if (validation) {
      Get.to(() => LoginPwdScreen(email: emailController.text));
    }
  }

  void onGoogleTap() {
    CommonUI.lottieLoader();
    // Removed Google Sign-In logic
    Get.back();
    CommonUI.snackBar(message: 'Google Sign-In is not available');
  }

  void onAppleTap() {
    // Removed Apple Sign-In logic
    Get.back();
    CommonUI.snackBar(message: 'Apple Sign-In is not available');
  }

  void onSignUpTap() {
    Get.to(() => const RegisterScreen());
  }

  void resetBtnClick(TextEditingController controller) async {
    resetFocusNode.unfocus();
    if (controller.text.isEmpty) {
      CommonUI.snackBar(message: S.current.pleaseEnterEmail);
      return;
    } else if (!GetUtils.isEmail(controller.text)) {
      CommonUI.snackBar(message: S.current.pleaseEnterValidEmailAddress);
      return;
    }
    Get.back();
    CommonUI.lottieLoader();
    // Removed password reset logic
    Get.back();
    CommonUI.snackBar(message: S.current.emailSentSuccessfully);
  }

  void onForgotPwdTap() {
    Get.to(
      () => ForgotPasswordScreen(
        resetBtnClick: resetBtnClick,
        resetFocusNode: resetFocusNode,
      ),
    );
  }

  void registrationApiCall({
    required String? email,
    required String? name,
    required int loginType,
  }) {
    CommonUI.lottieLoader();
    ApiProvider()
        .registration(
          email: email,
          fullName: name,
          deviceToken: tokenId,
          loginType: loginType,
          password: '',
        )
        .then((value) async {
          Get.back();
          if (value.status == true) {
            PrefService.userId = value.data?.id ?? -1;
            await PrefService.setLoginText(true);
            if (value.data?.latitude != null) {
              await PrefService.setLatitude("${value.data?.latitude}");
              await PrefService.setLongitude("${value.data?.longitude}");
            }
            await PrefService.saveUser(value.data);
            checkScreenCondition(value.data);
          }
        });
  }

  void checkScreenCondition(RegistrationUserData? data) {
    if (data == null) return;

    if (data.age == null) {
      Get.offAll(() => StartingProfileScreen(userData: data));
    } else {
      Get.offAll(() => const DashboardScreen());
    }
  }
}
