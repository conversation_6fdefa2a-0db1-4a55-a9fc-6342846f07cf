import 'package:flutter/material.dart';
import 'package:orange_ui/common/buttons.dart';
import 'package:orange_ui/common/starting_profile_top_text.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/starting_profile_screen/starting_profile_screen_view_model.dart';
import 'package:orange_ui/screen/starting_profile_screen/widget/text_field_area/text_fields_area.dart';
import 'package:orange_ui/screen/starting_profile_screen/widget/top_card_area.dart';
import 'package:orange_ui/screen/starting_profile_screen/widget/hobbies_clips.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:stacked/stacked.dart';
import 'package:dotted_border/dotted_border.dart';

class StartingProfileScreen extends StatelessWidget {
  final RegistrationUserData? userData;

  const StartingProfileScreen({super.key, required this.userData});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<StartingProfileScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder: () => StartingProfileScreenViewModel(userData),
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: ColorRes.white,
          body: GestureDetector(
            onTap: model.onAllScreenTap,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const StartingProfileTopText(),
                  const SizedBox(height: 16),
                  // Image carousel and add/remove buttons
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount:
                          model.imageFileList.length < 5
                              ? model.imageFileList.length + 1
                              : 5, // Max 5 images
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                            childAspectRatio: 1,
                          ),
                      itemBuilder: (context, index) {
                        // Show "Add" button if under 5 images
                        if (index == model.imageFileList.length &&
                            model.imageFileList.length < 5) {
                          return GestureDetector(
                            onTap: model.onImageAdd,
                            child: DottedBorder(
                              options: RoundedRectDottedBorderOptions(
                                radius: const Radius.circular(8),
                                dashPattern: [6, 4],
                                color: Colors.grey,
                              ),
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: const [
                                    Icon(
                                      Icons.add_a_photo,
                                      color: Colors.grey,
                                      size: 30,
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      "Add",
                                      style: TextStyle(color: Colors.grey),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        } else {
                          return Stack(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.file(
                                  model.imageFileList[index],
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                ),
                              ),
                              Positioned(
                                top: 4,
                                right: 4,
                                child: GestureDetector(
                                  onTap: () => model.onImageRemove(index),
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.black54,
                                    ),
                                    padding: const EdgeInsets.all(4),
                                    child: const Icon(
                                      Icons.close,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        }
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  TopCardArea(fullName: model.fullName),
                  const SizedBox(height: 18),
                  TextFieldsArea(
                    addressController: model.addressController,
                    bioController: model.bioController,
                    ageController: model.ageController,
                    gender: model.gender,
                    addressFocus: model.addressFocus,
                    ageFocus: model.ageFocus,
                    bioFocus: model.bioFocus,
                    onGenderTap: model.onGenderTap,
                    onTextFieldTap: model.onAllScreenTap,
                    showDropdown: model.showDropdown,
                    onGenderChange: model.onGenderChange,
                    bioError: model.bioError,
                    addressError: model.addressError,
                    ageError: model.ageError,
                  ),
                  const SizedBox(height: 16),
                  // Hobbies selection UI
                  model.hobbiesList.isEmpty
                      ? Center(child: Text("No hobbies available"))
                      : HobbiesClips(
                        hobbiesList: model.hobbiesList,
                        selectedList: model.selectedList,
                        onClipTap: model.onClipTap,
                      ),
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 16,
                      right: 16,
                      bottom: 22,
                    ),
                    child: SubmitButton2(
                      title: S.current.next,
                      onTap: model.onNextTap,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
