import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/subscription_plan_screen/subscription_plan_screen.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:orange_ui/utils/app_res.dart';
import 'package:orange_ui/utils/asset_res.dart';
import 'package:stacked/stacked.dart';

class StartingProfileScreenViewModel extends BaseViewModel {
  TextEditingController addressController = TextEditingController();
  TextEditingController bioController = TextEditingController();
  TextEditingController ageController = TextEditingController();

  FocusNode addressFocus = FocusNode();
  FocusNode bioFocus = FocusNode();
  FocusNode ageFocus = FocusNode();

  RegistrationUserData? userData;

  StartingProfileScreenViewModel(this.userData);

  String? fullName = '';
  String addressError = '';
  String bioError = '';
  String ageError = '';
  String latitude = '';
  String longitude = '';
  String gender = S.current.female;
  bool showDropdown = false;

  // Image selection state
  List<File> imageFileList = [];
  late PageController pageController;
  final ImagePicker imagePicker = ImagePicker();

  // Hobbies selection state
  List<Interest> hobbiesList = [];
  List<String> selectedList = [];

  void init() {
    getProfileApi();
    prefData();
    hobbiesList = [
      Interest(id: 1, title: 'Movies', image: AssetRes.movies),
      Interest(id: 2, title: 'Traveling', image: AssetRes.travel),
      Interest(id: 3, title: 'Cooking', image: AssetRes.chef),
      Interest(id: 4, title: 'Sports', image: AssetRes.fitness),
      Interest(id: 5, title: 'Music', image: AssetRes.music),
    ];

    notifyListeners();
    getInterestLocal();
    initPageController();
    getImageListLocal();
  }

  void initPageController() {
    pageController = PageController(initialPage: 0, viewportFraction: 1.05)
      ..addListener(() {
        onMainImageChange();
      });
  }

  void onMainImageChange() {
    notifyListeners();
  }

  void onAllScreenTap() {
    showDropdown = false;
    notifyListeners();
  }

  void getProfileApi() {
    fullName = userData?.fullname;
    notifyListeners();
  }

  void prefData() async {
    latitude = await PrefService.getLatitude() ?? '';
    longitude = await PrefService.getLongitude() ?? '';
  }

  // Local storage for hobbies
  void getInterestLocal() async {
    var interests = await PrefService.getInterest();
    if (interests?.data != null && interests!.data!.isNotEmpty) {
      hobbiesList = interests.data ?? [];
      notifyListeners();
    }
  }

  void onClipTap(String value) {
    bool selected = selectedList.contains(value);
    if (selected) {
      selectedList.remove(value);
    } else {
      selectedList.add(value);
    }
    notifyListeners();
  }

  // Local storage for images
  void getImageListLocal() async {
    List<String>? paths = await PrefService.getImagePaths();
    if (paths != null && paths.isNotEmpty) {
      imageFileList = paths.map((path) => File(path)).toList();
      notifyListeners();
    }
  }

  void onImageRemove(int index) {
    imageFileList.removeAt(index);
    saveImageListLocal();
    notifyListeners();
  }

  void onImageAdd() async {
    final selectedImages = await imagePicker.pickMultiImage(
      imageQuality: AppRes.quality,
      maxHeight: AppRes.maxHeight,
      maxWidth: AppRes.maxWidth,
    );
    if (selectedImages.isEmpty) return;
    for (var image in selectedImages) {
      imageFileList.add(File(image.path));
    }
    saveImageListLocal();
    notifyListeners();
  }

  Future<void> saveImageListLocal() async {
    List<String> paths = imageFileList.map((file) => file.path).toList();
    await PrefService.saveImagePaths(paths);
  }

  Future<void> saveProfileDataLocal() async {
    await PrefService.saveProfileData(
      bio: bioController.text,
      age: ageController.text,
      gender: gender,
      latitude: latitude,
      longitude: longitude,
      hobbies: selectedList,
    );
  }

  void onGenderTap() {
    addressFocus.unfocus();
    bioFocus.unfocus();
    ageFocus.unfocus();
    showDropdown = !showDropdown;
    notifyListeners();
  }

  void onGenderChange(String value) {
    gender = value;
    showDropdown = false;
    notifyListeners();
  }

  void onNextTap() async {
    if (ageController.text.isEmpty) {
      CommonUI.snackBar(message: "Please enter your age");
      ageFocus.requestFocus();
      return;
    }
    if (int.parse(ageController.text) < 18) {
      CommonUI.snackBar(message: "You must be 18 or older");
      return;
    }
    CommonUI.lottieLoader();
    // Save data locally instead of API call
    await saveProfileDataLocal();
    await saveImageListLocal();
    Get.back();
    checkScreenCondition(null);
  }

  void checkScreenCondition(RegistrationUserData? data) {
    // After profile completion, show subscription plan selection
    Get.to(() => const SubscriptionPlanScreen());
  }
}
