import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';

class CallHistoryItem extends StatelessWidget {
  final CallData callData;
  final VoidCallback onTap;
  final VoidCallback onCallTap;

  const CallHistoryItem({
    super.key,
    required this.callData,
    required this.onTap,
    required this.onCallTap,
  });

  @override
  Widget build(BuildContext context) {
    final String name =
        callData.isIncoming == true
            ? callData.callerName ?? 'Unknown'
            : callData.receiverName ?? 'Unknown';

    final String imageUrl =
        callData.isIncoming == true
            ? callData.callerImage ?? ''
            : callData.receiverImage ?? '';

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        child: Row(
          children: [
            // Profile image
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: ColorRes.grey.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: ClipOval(
                child:
                    imageUrl.isNotEmpty
                        ? CachedNetworkImage(
                          imageUrl: imageUrl,
                          fit: BoxFit.cover,
                          placeholder:
                              (context, url) => Container(
                                color: ColorRes.grey.withValues(alpha: 0.3),
                                child: const Icon(
                                  Icons.person,
                                  size: 25,
                                  color: ColorRes.grey,
                                ),
                              ),
                          errorWidget:
                              (context, url, error) => Container(
                                color: ColorRes.grey.withValues(alpha: 0.3),
                                child: const Icon(
                                  Icons.person,
                                  size: 25,
                                  color: ColorRes.grey,
                                ),
                              ),
                        )
                        : Container(
                          color: ColorRes.grey.withValues(alpha: 0.3),
                          child: const Icon(
                            Icons.person,
                            size: 25,
                            color: ColorRes.grey,
                          ),
                        ),
              ),
            ),

            const SizedBox(width: 15),

            // Call info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and call type
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontFamily: FontRes.medium,
                            color: ColorRes.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Icon(
                        callData.isVideoCall ? Icons.videocam : Icons.phone,
                        size: 16,
                        color: ColorRes.grey,
                      ),
                    ],
                  ),

                  const SizedBox(height: 4),

                  // Call status and time
                  Row(
                    children: [
                      Icon(
                        _getCallStatusIcon(),
                        size: 14,
                        color: _getCallStatusColor(),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getCallStatusText(),
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: FontRes.regular,
                          color: _getCallStatusColor(),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        _getFormattedTime(),
                        style: const TextStyle(
                          fontSize: 12,
                          fontFamily: FontRes.regular,
                          color: ColorRes.grey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(width: 10),

            // Call button
            GestureDetector(
              onTap: onCallTap,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: ColorRes.darkBlue.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  callData.isVideoCall ? Icons.videocam : Icons.phone,
                  size: 20,
                  color: ColorRes.darkBlue,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCallStatusIcon() {
    if (callData.isIncoming == true) {
      switch (callData.callStatus) {
        case CallStatus.missed:
          return Icons.call_received;
        case CallStatus.rejected:
          return Icons.call_received;
        default:
          return Icons.call_received;
      }
    } else {
      switch (callData.callStatus) {
        case CallStatus.missed:
          return Icons.call_made;
        case CallStatus.rejected:
          return Icons.call_made;
        default:
          return Icons.call_made;
      }
    }
  }

  Color _getCallStatusColor() {
    switch (callData.callStatus) {
      case CallStatus.missed:
        return ColorRes.red;
      case CallStatus.rejected:
        return ColorRes.red;
      case CallStatus.answered:
      case CallStatus.ended:
        return ColorRes.green;
      default:
        return ColorRes.grey;
    }
  }

  String _getCallStatusText() {
    if (callData.isIncoming == true) {
      switch (callData.callStatus) {
        case CallStatus.missed:
          return 'Missed';
        case CallStatus.rejected:
          return 'Declined';
        case CallStatus.answered:
        case CallStatus.ended:
          return callData.formattedDuration;
        default:
          return 'Incoming';
      }
    } else {
      switch (callData.callStatus) {
        case CallStatus.missed:
          return 'No answer';
        case CallStatus.rejected:
          return 'Declined';
        case CallStatus.answered:
        case CallStatus.ended:
          return callData.formattedDuration;
        default:
          return 'Outgoing';
      }
    }
  }

  String _getFormattedTime() {
    if (callData.startTime == null) return '';

    final now = DateTime.now();
    final callTime = callData.startTime!;
    final difference = now.difference(callTime);

    if (difference.inDays == 0) {
      // Today - show time
      return DateFormat('HH:mm').format(callTime);
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      // This week - show day
      return DateFormat('EEEE').format(callTime);
    } else {
      // Older - show date
      return DateFormat('dd/MM/yyyy').format(callTime);
    }
  }
}
