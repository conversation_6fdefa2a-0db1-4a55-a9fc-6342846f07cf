import 'package:flutter/material.dart';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/utils/color_res.dart';

class AudioCallControls extends StatelessWidget {
  final bool isMuted;
  final bool isSpeakerOn;
  final VoidCallback onMuteTap;
  final VoidCallback onSpeakerTap;
  final VoidCallback onEndCallTap;
  final bool isIncoming;
  final VoidCallback? onAcceptTap;
  final VoidCallback? onRejectTap;
  final int callStatus;

  const AudioCallControls({
    super.key,
    required this.isMuted,
    required this.isSpeakerOn,
    required this.onMuteTap,
    required this.onSpeakerTap,
    required this.onEndCallTap,
    required this.isIncoming,
    this.onAcceptTap,
    this.onRejectTap,
    required this.callStatus,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isIncoming && callStatus == CallStatus.calling)
            _buildIncomingCallControls()
          else
            _buildActiveCallControls(),
        ],
      ),
    );
  }

  Widget _buildIncomingCallControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Reject call
        _buildControlButton(
          icon: Icons.call_end,
          color: ColorRes.white,
          backgroundColor: ColorRes.red,
          onTap: onRejectTap ?? () {},
          size: 70,
        ),
        // Accept call
        _buildControlButton(
          icon: Icons.call,
          color: ColorRes.white,
          backgroundColor: ColorRes.green,
          onTap: onAcceptTap ?? () {},
          size: 70,
        ),
      ],
    );
  }

  Widget _buildActiveCallControls() {
    return Column(
      children: [
        // Top row - Mute and Speaker
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Mute/Unmute
            _buildControlButton(
              icon: isMuted ? Icons.mic_off : Icons.mic,
              color: isMuted ? ColorRes.red : ColorRes.white,
              backgroundColor: isMuted 
                  ? ColorRes.white.withValues(alpha: 0.2)
                  : ColorRes.white.withValues(alpha: 0.1),
              onTap: onMuteTap,
            ),
            
            // Speaker on/off
            _buildControlButton(
              icon: isSpeakerOn ? Icons.volume_up : Icons.volume_down,
              color: isSpeakerOn ? ColorRes.white : ColorRes.grey,
              backgroundColor: ColorRes.white.withValues(alpha: 0.1),
              onTap: onSpeakerTap,
            ),
          ],
        ),
        
        const SizedBox(height: 40),
        
        // Bottom row - End call (centered)
        _buildControlButton(
          icon: Icons.call_end,
          color: ColorRes.white,
          backgroundColor: ColorRes.red,
          onTap: onEndCallTap,
          size: 70,
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    Color? backgroundColor,
    required VoidCallback onTap,
    double size = 60,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: backgroundColor ?? ColorRes.white.withValues(alpha: 0.1),
          shape: BoxShape.circle,
          border: Border.all(
            color: ColorRes.white.withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: ColorRes.black.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: color,
          size: size * 0.4,
        ),
      ),
    );
  }
}
