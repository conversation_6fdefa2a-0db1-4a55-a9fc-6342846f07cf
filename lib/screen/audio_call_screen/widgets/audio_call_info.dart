import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';

class AudioCallInfo extends StatelessWidget {
  final CallData callData;
  final String callDuration;
  final bool isConnected;
  final bool isLoading;

  const AudioCallInfo({
    super.key,
    required this.callData,
    required this.callDuration,
    required this.isConnected,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    final String name = callData.isIncoming == true
        ? callData.callerName ?? 'Unknown'
        : callData.receiverName ?? 'Unknown';
    
    final String imageUrl = callData.isIncoming == true
        ? callData.callerImage ?? ''
        : callData.receiverImage ?? '';

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Profile image
        Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: ColorRes.white.withValues(alpha: 0.3),
              width: 4,
            ),
            boxShadow: [
              BoxShadow(
                color: ColorRes.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipOval(
            child: imageUrl.isNotEmpty
                ? CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: ColorRes.grey.withValues(alpha: 0.3),
                      child: const Icon(
                        Icons.person,
                        size: 80,
                        color: ColorRes.white,
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: ColorRes.grey.withValues(alpha: 0.3),
                      child: const Icon(
                        Icons.person,
                        size: 80,
                        color: ColorRes.white,
                      ),
                    ),
                  )
                : Container(
                    color: ColorRes.grey.withValues(alpha: 0.3),
                    child: const Icon(
                      Icons.person,
                      size: 80,
                      color: ColorRes.white,
                    ),
                  ),
          ),
        ),
        
        const SizedBox(height: 40),
        
        // Name
        Text(
          name,
          style: const TextStyle(
            color: ColorRes.white,
            fontSize: 28,
            fontFamily: FontRes.medium,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 12),
        
        // Call status or duration
        Text(
          isLoading
              ? 'Connecting...'
              : isConnected
                  ? callDuration
                  : _getCallStatusText(),
          style: const TextStyle(
            color: ColorRes.white,
            fontSize: 18,
            fontFamily: FontRes.regular,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 8),
        
        // Call type indicator
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: ColorRes.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: ColorRes.white.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.phone,
                color: ColorRes.white.withValues(alpha: 0.8),
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Audio Call',
                style: TextStyle(
                  color: ColorRes.white.withValues(alpha: 0.8),
                  fontSize: 14,
                  fontFamily: FontRes.regular,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getCallStatusText() {
    switch (callData.callStatus) {
      case CallStatus.calling:
        return callData.isIncoming == true ? 'Incoming call...' : 'Calling...';
      case CallStatus.answered:
        return 'Connected';
      case CallStatus.ended:
        return 'Call ended';
      case CallStatus.missed:
        return 'Missed call';
      case CallStatus.rejected:
        return 'Call rejected';
      default:
        return 'Connecting...';
    }
  }
}
