import 'dart:async';
import 'dart:developer' as dev;
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/model/setting.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/utils/firebase_res.dart';
import 'package:stacked/stacked.dart';

class AudioCallScreenViewModel extends BaseViewModel {
  final CallData callData;
  final RegistrationUserData? userData;
  final Appdata? settingData;

  AudioCallScreenViewModel({
    required this.callData,
    this.userData,
    this.settingData,
  });

  FirebaseFirestore db = FirebaseFirestore.instance;

  int? _remoteUid;
  Timer? _callTimer;
  Timer? _connectionTimer;
  Timer? _remoteUserTimer;
  final Stopwatch _stopwatch = Stopwatch();

  bool isLoading = false;
  bool localUserJoined = false;
  bool isMuted = false;
  bool isSpeakerOn = false;
  bool isConnected = false;

  String callDuration = '00:00';

  void init() {
    initializeSimulation();
    startCallTimer();
  }

  Future<void> initializeSimulation() async {
    isLoading = true;
    notifyListeners();

    try {
      // Simulate connection delay (1-3 seconds)
      final connectionDelay = Random().nextInt(2000) + 1000;

      _connectionTimer = Timer(Duration(milliseconds: connectionDelay), () {
        // Simulate local user joined
        localUserJoined = true;
        isLoading = false;
        notifyListeners();

        // Simulate remote user joining after connection
        final remoteUserDelay =
            callData.isIncoming == true ? 0 : (Random().nextInt(3000) + 2000);

        _remoteUserTimer = Timer(Duration(milliseconds: remoteUserDelay), () {
          // Simulate remote user joined
          _remoteUid = Random().nextInt(1000) + 1;
          isConnected = true;
          _startCallDuration();
          notifyListeners();
        });
      });

      // Update call status to answered if this is an incoming call
      if (callData.isIncoming == true) {
        await _updateCallStatus(CallStatus.answered);
      }
    } catch (e) {
      isLoading = false;
      notifyListeners();
      CommonUI.snackBar(message: 'Failed to initialize call: $e');
      Get.back();
    }
  }

  void startCallTimer() {
    _callTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_stopwatch.isRunning) {
        final duration = _stopwatch.elapsed;
        final minutes = duration.inMinutes;
        final seconds = duration.inSeconds % 60;
        callDuration =
            '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
        notifyListeners();
      }
    });
  }

  void _startCallDuration() {
    _stopwatch.start();
    callData.startTime = DateTime.now();
  }

  Future<void> _updateCallStatus(int status) async {
    try {
      callData.callStatus = status;
      await db.collection(FirebaseRes.calls).doc(callData.callId).update({
        'callStatus': status,
        if (status == CallStatus.answered)
          'startTime': DateTime.now().millisecondsSinceEpoch,
        if (status == CallStatus.ended)
          'endTime': DateTime.now().millisecondsSinceEpoch,
        if (status == CallStatus.ended)
          'duration': _stopwatch.elapsed.inSeconds,
      });
    } catch (e) {
      dev.log('Error updating call status: $e');
    }
  }

  void onMuteTap() {
    isMuted = !isMuted;
    // Simulate mute action
    dev.log('Audio ${isMuted ? 'muted' : 'unmuted'}');
    notifyListeners();
  }

  void onSpeakerTap() {
    isSpeakerOn = !isSpeakerOn;
    // Simulate speaker toggle action
    dev.log('Speaker ${isSpeakerOn ? 'on' : 'off'}');
    notifyListeners();
  }

  void onAcceptCallTap() async {
    await _updateCallStatus(CallStatus.answered);
    _startCallDuration();
  }

  void onRejectCallTap() async {
    await _updateCallStatus(CallStatus.rejected);
    _endCall();
  }

  void onEndCallTap() async {
    await _updateCallStatus(CallStatus.ended);
    _endCall();
  }

  void _endCall() {
    _stopwatch.stop();
    callData.endTime = DateTime.now();
    callData.duration = _stopwatch.elapsed.inSeconds;

    _callTimer?.cancel();
    _connectionTimer?.cancel();
    _remoteUserTimer?.cancel();

    // Simulate ending call
    dev.log('Audio call ended');

    Get.back();
  }

  @override
  void dispose() {
    _callTimer?.cancel();
    _connectionTimer?.cancel();
    _remoteUserTimer?.cancel();
    super.dispose();
  }
}
