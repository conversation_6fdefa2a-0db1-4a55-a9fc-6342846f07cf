import 'package:flutter/material.dart';
import 'package:orange_ui/model/call/call_data.dart';
import 'package:orange_ui/model/setting.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/audio_call_screen/audio_call_screen_view_model.dart';
import 'package:orange_ui/screen/audio_call_screen/widgets/audio_call_controls.dart';
import 'package:orange_ui/screen/audio_call_screen/widgets/audio_call_info.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:stacked/stacked.dart';

class AudioCallScreen extends StatelessWidget {
  final CallData callData;
  final RegistrationUserData? userData;
  final Appdata? settingData;

  const AudioCallScreen({
    super.key,
    required this.callData,
    this.userData,
    this.settingData,
  });

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<AudioCallScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder:
          () => AudioCallScreenViewModel(
            callData: callData,
            userData: userData,
            settingData: settingData,
          ),
      builder: (context, model, child) {
        return PopScope(
          canPop: false,
          child: Scaffold(
            body: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    ColorRes.darkBlue.withValues(alpha: 0.8),
                    ColorRes.black,
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    const SizedBox(height: 60),

                    // Profile image and call info
                    Expanded(
                      child: AudioCallInfo(
                        callData: model.callData,
                        callDuration: model.callDuration,
                        isConnected: model.isConnected,
                        isLoading: model.isLoading,
                      ),
                    ),

                    // Call controls
                    AudioCallControls(
                      isMuted: model.isMuted,
                      isSpeakerOn: model.isSpeakerOn,
                      onMuteTap: model.onMuteTap,
                      onSpeakerTap: model.onSpeakerTap,
                      onEndCallTap: model.onEndCallTap,
                      isIncoming: model.callData.isIncoming ?? false,
                      onAcceptTap: model.onAcceptCallTap,
                      onRejectTap: model.onRejectCallTap,
                      callStatus:
                          model.callData.callStatus ?? CallStatus.calling,
                    ),

                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
