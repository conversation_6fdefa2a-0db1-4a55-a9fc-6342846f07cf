import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:orange_ui/utils/pref_res.dart';
import 'package:stacked/stacked.dart';

class ChangePasswordScreenViewModel extends BaseViewModel {
  TextEditingController currentPasswordController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();

  FocusNode currentPasswordFocus = FocusNode();
  FocusNode newPasswordFocus = FocusNode();
  FocusNode confirmPasswordFocus = FocusNode();

  bool showCurrentPassword = false;
  bool showNewPassword = false;
  bool showConfirmPassword = false;

  String currentPasswordError = '';
  String newPasswordError = '';
  String confirmPasswordError = '';

  bool isLoading = false;

  void init() {
    // Initialize any required data
  }

  void toggleCurrentPasswordVisibility() {
    showCurrentPassword = !showCurrentPassword;
    notifyListeners();
  }

  void toggleNewPasswordVisibility() {
    showNewPassword = !showNewPassword;
    notifyListeners();
  }

  void toggleConfirmPasswordVisibility() {
    showConfirmPassword = !showConfirmPassword;
    notifyListeners();
  }

  void onUpdatePasswordTap() {
    _clearErrors();
    
    if (_validateInputs()) {
      _updatePassword();
    } else {
      notifyListeners();
    }
  }

  void _clearErrors() {
    currentPasswordError = '';
    newPasswordError = '';
    confirmPasswordError = '';
  }

  bool _validateInputs() {
    bool isValid = true;

    // Validate current password
    if (currentPasswordController.text.isEmpty) {
      currentPasswordError = S.current.enterCurrentPassword;
      isValid = false;
    }

    // Validate new password
    if (newPasswordController.text.isEmpty) {
      newPasswordError = S.current.enterNewPassword;
      isValid = false;
    } else if (newPasswordController.text.length < 6) {
      newPasswordError = 'Password must be at least 6 characters';
      isValid = false;
    }

    // Validate confirm password
    if (confirmPasswordController.text.isEmpty) {
      confirmPasswordError = S.current.enterConfirmNewPassword;
      isValid = false;
    } else if (newPasswordController.text != confirmPasswordController.text) {
      confirmPasswordError = S.current.passwordsDoNotMatch;
      isValid = false;
    }

    // Check if new password is different from current password
    if (isValid && currentPasswordController.text == newPasswordController.text) {
      newPasswordError = 'New password must be different from current password';
      isValid = false;
    }

    return isValid;
  }

  void _updatePassword() async {
    isLoading = true;
    notifyListeners();

    try {
      // Get stored password to verify current password
      String? storedPassword = await PrefService.getString(PrefConst.password);
      
      if (storedPassword != currentPasswordController.text) {
        currentPasswordError = S.current.incorrectCurrentPassword;
        isLoading = false;
        notifyListeners();
        return;
      }

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // In a real app, you would call your API to update the password
      // For now, we'll just update the stored password
      await PrefService.saveString(PrefConst.password, newPasswordController.text);

      isLoading = false;
      notifyListeners();

      // Show success message
      CommonUI.snackBarWidget(S.current.passwordChanged);
      
      // Clear form
      _clearForm();
      
      // Go back to previous screen
      Get.back();

    } catch (e) {
      isLoading = false;
      notifyListeners();
      CommonUI.snackBarWidget('Failed to update password. Please try again.');
    }
  }

  void _clearForm() {
    currentPasswordController.clear();
    newPasswordController.clear();
    confirmPasswordController.clear();
    _clearErrors();
  }

  @override
  void dispose() {
    currentPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    currentPasswordFocus.dispose();
    newPasswordFocus.dispose();
    confirmPasswordFocus.dispose();
    super.dispose();
  }
}
