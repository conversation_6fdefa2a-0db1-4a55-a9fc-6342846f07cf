import 'package:flutter/material.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/common/top_bar_area.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';
import 'package:stacked/stacked.dart';
import 'change_password_screen_view_model.dart';

class ChangePasswordScreen extends StatelessWidget {
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<ChangePasswordScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder: () => ChangePasswordScreenViewModel(),
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: ColorRes.white,
          body: SafeArea(
            top: false,
            child: Column(
              children: [
                TopBarArea(title2: S.current.changePassword),
                Container(
                  height: 0.5,
                  margin: const EdgeInsets.symmetric(horizontal: 7),
                  width: MediaQuery.of(context).size.width,
                  color: ColorRes.grey5,
                ),
                Expanded(
                  child: model.isLoading
                      ? Center(child: CommonUI.lottieWidget())
                      : Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 20),
                              Text(
                                S.current.changePasswordDesc,
                                style: const TextStyle(
                                  color: ColorRes.grey,
                                  fontSize: 14,
                                  fontFamily: FontRes.regular,
                                ),
                              ),
                              const SizedBox(height: 32),
                              _buildPasswordField(
                                controller: model.currentPasswordController,
                                focusNode: model.currentPasswordFocus,
                                label: S.current.currentPassword,
                                hint: S.current.enterCurrentPassword,
                                error: model.currentPasswordError,
                                isObscure: !model.showCurrentPassword,
                                onToggleVisibility: model.toggleCurrentPasswordVisibility,
                              ),
                              const SizedBox(height: 20),
                              _buildPasswordField(
                                controller: model.newPasswordController,
                                focusNode: model.newPasswordFocus,
                                label: S.current.newPassword,
                                hint: S.current.enterNewPassword,
                                error: model.newPasswordError,
                                isObscure: !model.showNewPassword,
                                onToggleVisibility: model.toggleNewPasswordVisibility,
                              ),
                              const SizedBox(height: 20),
                              _buildPasswordField(
                                controller: model.confirmPasswordController,
                                focusNode: model.confirmPasswordFocus,
                                label: S.current.confirmNewPassword,
                                hint: S.current.enterConfirmNewPassword,
                                error: model.confirmPasswordError,
                                isObscure: !model.showConfirmPassword,
                                onToggleVisibility: model.toggleConfirmPasswordVisibility,
                              ),
                              const Spacer(),
                              _buildUpdateButton(model),
                              const SizedBox(height: 16),
                            ],
                          ),
                        ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String label,
    required String hint,
    required String error,
    required bool isObscure,
    required VoidCallback onToggleVisibility,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: ColorRes.darkBlue,
            fontSize: 14,
            fontFamily: FontRes.semiBold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: ColorRes.grey10,
            border: error.isNotEmpty
                ? Border.all(color: ColorRes.red, width: 1)
                : null,
          ),
          child: TextField(
            controller: controller,
            focusNode: focusNode,
            obscureText: isObscure,
            style: const TextStyle(
              fontFamily: FontRes.semiBold,
              fontSize: 14,
            ),
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              border: InputBorder.none,
              hintText: error.isEmpty ? hint : error,
              hintStyle: TextStyle(
                color: error.isEmpty ? ColorRes.dimGrey2 : ColorRes.red,
                fontSize: 14,
                fontFamily: FontRes.regular,
              ),
              suffixIcon: InkWell(
                onTap: onToggleVisibility,
                child: Padding(
                  padding: const EdgeInsets.all(14),
                  child: Text(
                    isObscure ? S.current.view : S.current.hide,
                    style: const TextStyle(
                      color: ColorRes.darkGrey,
                      fontSize: 13,
                      fontFamily: FontRes.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUpdateButton(ChangePasswordScreenViewModel model) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: model.onUpdatePasswordTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorRes.blue,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: Text(
          S.current.updatePassword,
          style: const TextStyle(
            color: ColorRes.white,
            fontSize: 16,
            fontFamily: FontRes.semiBold,
          ),
        ),
      ),
    );
  }
}
