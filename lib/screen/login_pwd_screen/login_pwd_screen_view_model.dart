import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:orange_ui/common/common_ui.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/dashboard/dashboard_screen.dart';
import 'package:orange_ui/screen/forgot_password_screen/forgot_password_screen.dart';
import 'package:orange_ui/screen/starting_profile_screen/starting_profile_screen.dart';
import 'package:orange_ui/service/firebase_notification_manager.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:stacked/stacked.dart';

class LoginPwdScreenViewModel extends BaseViewModel {
  String? tokenId;
  String email = '';
  TextEditingController pwdController = TextEditingController();
  FocusNode pwdFocus = FocusNode();
  String pwdError = "";
  bool showPwd = false;
  bool isVerified = false;
  RegistrationUserData? userData;
  FocusNode resetFocusNode = FocusNode();
  bool isResetBtnVisible = false;

  void init(String email) {
    FirebaseNotificationManager.shared.getNotificationToken((token) {
      tokenId = token;
    });
    this.email = email;
  }

  void onViewBtnTap() {
    showPwd = !showPwd;
    notifyListeners();
  }

  void onBackBtnTap() {
    Get.back();
  }

  Future<void> onContinueTap() async {
    bool validation = inValid();
    pwdFocus.unfocus();
    if (validation) {
      // Fetch user from local storage
      RegistrationUserData? localUser = await PrefService.getUserData();
      if (localUser != null &&
          localUser.identity == email &&
          localUser.password == pwdController.text) {
        await PrefService.setLoginText(true);
        PrefService.userId = localUser.id ?? -1;
        await PrefService.saveUser(localUser);
        await PrefService.setFullName(localUser.fullname ?? '');
        checkScreenCondition(localUser);
      } else {
        CommonUI.snackBarWidget(S.current.incorrectPasswordOrUserid);
      }
    }
  }

  bool inValid() {
    if (pwdController.text == "") {
      pwdError = S.current.enterPassword;
      return false;
    } else {
      return true;
    }
  }

  void resetBtnClick(TextEditingController controller) async {
    resetFocusNode.unfocus();
    if (controller.text.isEmpty) {
      CommonUI.snackBar(message: S.current.pleaseEnterEmail);
      return;
    } else if (!GetUtils.isEmail(controller.text)) {
      CommonUI.snackBar(message: S.current.pleaseEnterValidEmailAddress);
      return;
    }
    Get.back();
    CommonUI.lottieLoader();
    controller.clear();
    Get.back();
    CommonUI.snackBar(message: S.current.emailSentSuccessfully);
  }

  void onForgotPwdTap() {
    Get.to(
      () => ForgotPasswordScreen(
        resetBtnClick: resetBtnClick,
        resetFocusNode: resetFocusNode,
      ),
    );
  }

  void checkScreenCondition(RegistrationUserData? data) {
    if (data?.age == null) {
      Get.offAll(() => StartingProfileScreen(userData: data));
    } else {
      Get.offAll(() => const DashboardScreen());
    }
  }
}
