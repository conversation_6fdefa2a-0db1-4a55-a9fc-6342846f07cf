import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/asset_res.dart';
import 'package:confetti/confetti.dart';

class UserDetailScreen extends StatefulWidget {
  const UserDetailScreen({super.key});

  @override
  State<UserDetailScreen> createState() => _UserDetailScreenState();
}

class _UserDetailScreenState extends State<UserDetailScreen> {
  late ConfettiController _confettiController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(
      duration: const Duration(seconds: 1),
    );
  }

  @override
  void dispose() {
    _confettiController.dispose();
    super.dispose();
  }

  void _onSuperlike() {
    _confettiController.play();
  }

  @override
  Widget build(BuildContext context) {
    final String name = "<PERSON>";
    final String location = "Berlin, Germany";
    final String bio =
        "Passionate traveler, foodie, and book lover. Looking for meaningful conversations and new adventures.";
    final List<String> imageUrls = [
      'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e',
      'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d',
      'https://images.unsplash.com/photo-1524504388940-b1c1722653e1',
    ];
    final List<String> interests = [
      'Hiking',
      'Photography',
      'Jazz Music',
      'Cooking',
      'Yoga',
    ];

    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image Carousel
                Column(
                  children: [
                    Stack(
                      children: [
                        CarouselSlider(
                          options: CarouselOptions(
                            height: 400,
                            viewportFraction: 1.0,
                            enableInfiniteScroll: false,
                            enlargeCenterPage: false,
                            onPageChanged: (index, reason) {
                              setState(() {
                                _currentIndex = index;
                              });
                            },
                          ),
                          items:
                              imageUrls.map((url) {
                                return Builder(
                                  builder: (BuildContext context) {
                                    return ClipRRect(
                                      borderRadius: const BorderRadius.only(
                                        bottomLeft: Radius.circular(20),
                                        bottomRight: Radius.circular(20),
                                      ),
                                      child: Image.network(
                                        url,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        fit: BoxFit.cover,
                                      ),
                                    );
                                  },
                                );
                              }).toList(),
                        ),

                        Positioned(
                          top: 40,
                          left: 16,
                          right: 16,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CircleAvatar(
                                backgroundColor: Colors.white,
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.arrow_back_ios_new,
                                    color: ColorRes.darkBlue,
                                  ),
                                  onPressed: () => Navigator.pop(context),
                                ),
                              ),
                              PopupMenuButton<String>(
                                icon: const Icon(
                                  Icons.more_vert,
                                  color: Colors.white,
                                ),
                                itemBuilder:
                                    (BuildContext context) => [
                                      const PopupMenuItem(
                                        value: 'report',
                                        child: Text('Report User'),
                                      ),
                                      const PopupMenuItem(
                                        value: 'block',
                                        child: Text('Block User'),
                                      ),
                                    ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children:
                          imageUrls.asMap().entries.map((entry) {
                            return Container(
                              width: 8.0,
                              height: 8.0,
                              margin: const EdgeInsets.symmetric(
                                horizontal: 4.0,
                                vertical: 8.0,
                              ),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color:
                                    _currentIndex == entry.key
                                        ? ColorRes.blue
                                        : ColorRes.blue.withOpacity(0.3),
                              ),
                            );
                          }).toList(),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(
                            Icons.location_on,
                            size: 20,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            location,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        "2 km away • Active 10 min ago",
                        style: TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // Like button
                          GestureDetector(
                            onTap: () {},
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: ColorRes.blue,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: ColorRes.blue.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.favorite,
                                color: ColorRes.white,
                                size: 30,
                              ),
                            ),
                          ),
                          // Superlike button
                          GestureDetector(
                            onTap: _onSuperlike,
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.yellow,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.yellow.withOpacity(0.6),
                                    blurRadius: 12,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.bolt,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                          ),
                          // Chat button
                          GestureDetector(
                            onTap: () {},
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: ColorRes.white,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: ColorRes.blue,
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: ColorRes.blue.withOpacity(0.10),
                                    blurRadius: 6,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Image.asset(
                                  AssetRes.message,
                                  width: 24,
                                  height: 24,
                                  color: ColorRes.blue,
                                ),
                              ),
                            ),
                          ),
                          // Call button
                          GestureDetector(
                            onTap: () {},
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: ColorRes.white,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: ColorRes.green,
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: ColorRes.green.withOpacity(0.10),
                                    blurRadius: 6,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Image.asset(
                                  AssetRes.call,
                                  width: 24,
                                  height: 24,
                                  color: ColorRes.green,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        "About",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(bio, style: const TextStyle(fontSize: 16)),
                      const SizedBox(height: 16),
                      const Text(
                        "Interests",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children:
                            interests
                                .map(
                                  (interest) => Chip(
                                    label: Text(
                                      interest,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    backgroundColor: Colors.white,
                                    shape: StadiumBorder(
                                      side: BorderSide(color: ColorRes.blue),
                                    ),
                                  ),
                                )
                                .toList(),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Align(
            alignment: Alignment.center,
            child: ConfettiWidget(
              confettiController: _confettiController,
              maxBlastForce: 20,
              minBlastForce: 8,
              emissionFrequency: 0.05,
              numberOfParticles: 20,
              gravity: 0.1,
            ),
          ),
        ],
      ),
    );
  }
}
