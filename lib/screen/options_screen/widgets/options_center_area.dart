import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/screen/blocked_profiles_screen/blocked_profiles_screen.dart';
import 'package:orange_ui/screen/subscription_settings_screen/subscription_settings_screen.dart';
import 'package:orange_ui/screen/change_password_screen/change_password_screen.dart';
import 'package:orange_ui/utils/asset_res.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';

class OptionsCenterArea extends StatelessWidget {
  final int notificationStatus;
  final int showMeOnMapStatus;
  final int goAnonymousStatus;
  final VoidCallback onApplyForVerTap;
  final VoidCallback onNotificationTap;
  final VoidCallback onShowMeOnMapTap;
  final VoidCallback onAnonymousTap;
  final int? verification;

  const OptionsCenterArea({
    super.key,
    required this.notificationStatus,
    required this.showMeOnMapStatus,
    required this.goAnonymousStatus,
    required this.onNotificationTap,
    required this.onShowMeOnMapTap,
    required this.onAnonymousTap,
    required this.onApplyForVerTap,
    required this.verification,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 6),
        
        Visibility(
          visible: verification == 0,
          child: TopOptionCard(
            title: S.current.verification,
            onTap: onApplyForVerTap,
            widget: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10.0),
              child: Image.asset(AssetRes.icBlueTick, height: 28, width: 28),
            ),
          ),
        ),
        Visibility(
          visible:
              (verification == 2
                  ? false
                  : true && verification == 0
                  ? false
                  : true),
          child: Stack(
            children: [
              Container(
                height: 54,
                width: Get.width,
                margin: const EdgeInsets.only(top: 3, bottom: 3),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: Image.asset(AssetRes.map1),
                ),
              ),
              Container(
                height: 54,
                width: Get.width,
                margin: const EdgeInsets.only(top: 3, bottom: 3),
                decoration: BoxDecoration(
                  color: ColorRes.darkGrey5.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(15),
                ),
              ),
              Container(
                height: 54,
                width: Get.width,
                margin: const EdgeInsets.only(top: 3, bottom: 3),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaY: 15,
                      sigmaX: 15,
                      tileMode: TileMode.mirror,
                    ),
                    child: Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Image.asset(
                            AssetRes.tickMark,
                            height: 25,
                            width: 25,
                          ),
                        ),
                        Text(
                          S.current.liveVerification,
                          style: const TextStyle(
                            color: ColorRes.davyGrey,
                            fontSize: 15,
                            fontFamily: FontRes.semiBold,
                          ),
                        ),
                        const Spacer(),
                        Container(
                          height: 36.17,
                          width: 112,
                          margin: const EdgeInsets.symmetric(horizontal: 10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30),
                            color:
                                verification == 0
                                    ? ColorRes.darkBlue.withValues(alpha: 0.20)
                                    : verification == 1
                                    ? ColorRes.blue.withValues(alpha: 0.20)
                                    : ColorRes.lightGreen.withValues(
                                      alpha: 0.20,
                                    ),
                          ),
                          child: Center(
                            child: Text(
                              verification == 0
                                  ? S.current.notEligible
                                  : verification == 1
                                  ? S.current.pending
                                  : S.current.eligible,
                              style: TextStyle(
                                fontSize: 12,
                                color:
                                    verification == 0
                                        ? ColorRes.darkBlue
                                        : verification == 1
                                        ? ColorRes.blue
                                        : ColorRes.green2,
                                fontFamily: FontRes.semiBold,
                                letterSpacing: 0.8,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
       
        TopOptionCard(
          onTap: () {
            Get.to(() => const BlockedProfilesScreen());
          },
          title: S.of(context).blockedProfiles,
          widget: const CircleImage(image: AssetRes.icBlock),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 18, bottom: 9),
          child: Text(
            S.current.accountSettings,
            style: const TextStyle(
              color: ColorRes.grey23,
              fontSize: 14,
              fontFamily: FontRes.bold,
              letterSpacing: 0.8,
            ),
          ),
        ),
        TopOptionCard(
          onTap: () {
            Get.to(() => const SubscriptionSettingsScreen());
          },
          title: S.current.subscription,
          widget: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Image.asset(
              AssetRes.diamond,
              height: 28,
              width: 28,
            ),
          ),
        ),
        TopOptionCard(
          onTap: () {
            Get.to(() => const ChangePasswordScreen());
          },
          title: S.current.changePassword,
          widget: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Icon(Icons.lock_outline, size: 28, color: ColorRes.blue),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 18, bottom: 9),
          child: Text(
            S.current.privacy,
            style: const TextStyle(
              color: ColorRes.grey23,
              fontSize: 14,
              fontFamily: FontRes.bold,
              letterSpacing: 0.8,
            ),
          ),
        ),
        PermissionTiles(
          title: S.current.pushNotification,
          subTitle: S.current.notificationData,
          enable: notificationStatus == 1,
          onTap: onNotificationTap,
        ),
        PermissionTiles(
          title: S.current.switchMap,
          subTitle: S.current.switchMapData,
          enable: showMeOnMapStatus == 1,
          onTap: onShowMeOnMapTap,
        ),
        PermissionTiles(
          title: S.current.anonymous,
          subTitle: S.current.anonymousData,
          enable: goAnonymousStatus == 1,
          onTap: onAnonymousTap,
        ),
      ],
    );
  }
}

class CircleImage extends StatelessWidget {
  final String image;

  const CircleImage({super.key, required this.image});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(6.0),
      height: 28,
      width: 28,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        color: ColorRes.darkBlue.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Image.asset(image),
    );
  }
}

class PermissionTiles extends StatelessWidget {
  final String title;
  final String subTitle;
  final bool enable;
  final VoidCallback onTap;

  const PermissionTiles({
    super.key,
    required this.title,
    required this.subTitle,
    required this.enable,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      margin: const EdgeInsets.only(top: 3, bottom: 3),
      padding: const EdgeInsets.fromLTRB(14, 14, 8, 18),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: ColorRes.grey10,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 15,
                  color: ColorRes.davyGrey,
                  fontFamily: FontRes.semiBold,
                ),
              ),
              const SizedBox(height: 3),
              SizedBox(
                width: Get.width - 90,
                child: Text(
                  subTitle,
                  style: const TextStyle(color: ColorRes.grey20),
                ),
              ),
            ],
          ),
          InkWell(
            borderRadius: BorderRadius.circular(30),
            onTap: onTap,
            child: Container(
              height: 25,
              width: 36,
              padding: const EdgeInsets.symmetric(horizontal: 3.5),
              alignment: enable ? Alignment.centerRight : Alignment.centerLeft,
              decoration: BoxDecoration(
                color: ColorRes.grey,
                borderRadius: BorderRadius.circular(30),
                gradient:
                    enable
                        ? const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [ColorRes.blue, ColorRes.darkBlue],
                        )
                        : null,
              ),
              child: Container(
                height: 19,
                width: 19,
                decoration: const BoxDecoration(
                  color: ColorRes.white,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TopOptionCard extends StatelessWidget {
  final VoidCallback onTap;
  final String title;
  final Widget widget;

  const TopOptionCard({
    super.key,
    required this.onTap,
    required this.title,
    required this.widget,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 49,
        margin: const EdgeInsets.symmetric(vertical: 3),
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: ColorRes.grey10,
        ),
        child: Row(
          children: [
            widget,
            Text(
              title,
              style: const TextStyle(
                color: ColorRes.davyGrey,
                fontSize: 15,
                fontFamily: FontRes.semiBold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
