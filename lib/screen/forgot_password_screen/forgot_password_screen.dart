import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:orange_ui/common/buttons.dart';
import 'package:orange_ui/generated/l10n.dart';
import 'package:orange_ui/utils/asset_res.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';

class ForgotPasswordScreen extends StatefulWidget {
  final Function(TextEditingController) resetBtnClick;
  final FocusNode resetFocusNode;

  const ForgotPasswordScreen({
    super.key,
    required this.resetBtnClick,
    required this.resetFocusNode,
  });

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  late TextEditingController emailController;

  @override
  void initState() {
    super.initState();
    emailController = TextEditingController();
  }

  @override
  void dispose() {
    emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(Icons.arrow_back_ios_new, color: ColorRes.white),
        ),
      ),
      body: Stack(
        children: [
          // Background image with blue overlay
          SingleChildScrollView(
            child: Image.asset(
              AssetRes.loginBG,
              height: Get.height,
              width: Get.width,
              fit: BoxFit.cover,
              color: ColorRes.blue,
              colorBlendMode: BlendMode.modulate,
            ),
          ),
          SizedBox(
            height: Get.height,
            width: Get.width,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: Get.height / 6),
                  Image.asset(AssetRes.themeLabelWhite, width: Get.width * 0.6),

                  SizedBox(height: Get.height / 13),
                  Row(
                    children: [
                      const SizedBox(width: 20),

                      Text(
                        S.current.forgotPassword,
                        style: const TextStyle(
                          color: ColorRes.white,
                          fontSize: 25,
                          fontFamily: FontRes.extraBold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Container(
                    margin: const EdgeInsets.only(
                      left: 8,
                      right: 8,
                      bottom: 30,
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: 25,
                      horizontal: 25,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: ColorRes.darkBlue.withValues(alpha: 0.55),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          S
                              .current
                              .enterYourMailOnWhichYouHaveNcreatedAnAccount,
                          style: const TextStyle(
                            color: ColorRes.white,
                            fontSize: 13,
                          ),
                          textAlign: TextAlign.justify,
                        ),
                        const SizedBox(height: 20),
                        Container(
                          height: 44,
                          width: Get.width,
                          decoration: BoxDecoration(
                            color: ColorRes.white,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: TextField(
                            controller: emailController,
                            focusNode: widget.resetFocusNode,
                            style: const TextStyle(
                              fontFamily: FontRes.semiBold,
                            ),
                            decoration: InputDecoration(
                              contentPadding: const EdgeInsets.only(left: 14),
                              border: InputBorder.none,
                              hintText: S.current.email,
                              hintStyle: TextStyle(
                                color: ColorRes.dimGrey2,
                                fontSize: 14,
                                fontFamily: FontRes.semiBold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 15),
                        SubmitButton1(
                          title: S.current.reset,
                          onTap: () => widget.resetBtnClick(emailController),
                        ),
                        SizedBox(height: 30),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
