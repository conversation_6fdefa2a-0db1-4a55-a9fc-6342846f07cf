import 'package:flutter/material.dart';
import 'package:orange_ui/screen/save_screen/save_screen_view_model.dart';
import 'package:orange_ui/utils/color_res.dart';
import 'package:orange_ui/utils/font_res.dart';
import 'package:stacked/stacked.dart';

class SaveScreen extends StatelessWidget {
  const SaveScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ViewModelBuilder<SaveScreenViewModel>.reactive(
      onViewModelReady: (model) {
        model.init();
      },
      viewModelBuilder: () => SaveScreenViewModel(),
      builder: (context, model, child) {
        return Scaffold(
          backgroundColor: ColorRes.white,
          body: SafeArea(
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Text(
                        'Favorites',
                        style: TextStyle(
                          fontSize: 24,
                          fontFamily: FontRes.bold,
                          color: ColorRes.darkBlue,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${model.savedProfiles.length} saved',
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: FontRes.regular,
                          color: ColorRes.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Content
                Expanded(
                  child: model.savedProfiles.isEmpty 
                    ? _buildEmptyState()
                    : _buildSavedProfilesList(model),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_outline,
            size: 80,
            color: ColorRes.grey,
          ),
          const SizedBox(height: 24),
          Text(
            'No Saved Profiles',
            style: TextStyle(
              fontSize: 20,
              fontFamily: FontRes.bold,
              color: ColorRes.darkBlue,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Profiles you like will appear here.\nStart swiping to find your matches!',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontFamily: FontRes.regular,
              color: ColorRes.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavedProfilesList(SaveScreenViewModel model) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: model.savedProfiles.length,
      itemBuilder: (context, index) {
        final profile = model.savedProfiles[index];
        return GestureDetector(
          onTap: () => model.onProfileTap(profile),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: ColorRes.grey.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // Profile Image
                  Image.network(
                    profile.imageUrls.isNotEmpty ? profile.imageUrls[0] : '',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: ColorRes.grey.withOpacity(0.3),
                        child: Icon(
                          Icons.person,
                          size: 50,
                          color: ColorRes.grey,
                        ),
                      );
                    },
                  ),
                  
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),
                  
                  // Profile info
                  Positioned(
                    bottom: 12,
                    left: 12,
                    right: 12,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '${profile.name}, ${profile.age}',
                          style: TextStyle(
                            fontSize: 16,
                            fontFamily: FontRes.bold,
                            color: ColorRes.white,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (profile.location.isNotEmpty) ...[
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.location_on,
                                size: 12,
                                color: ColorRes.white.withOpacity(0.8),
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  profile.location,
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontFamily: FontRes.regular,
                                    color: ColorRes.white.withOpacity(0.8),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                  
                  // Remove button
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: () => model.onRemoveProfile(profile),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.favorite,
                          size: 20,
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
