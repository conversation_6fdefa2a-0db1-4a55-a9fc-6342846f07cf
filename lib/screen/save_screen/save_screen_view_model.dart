import 'package:stacked/stacked.dart';
import 'package:orange_ui/service/saved_profiles_service.dart';
import 'package:orange_ui/screen/home_screen/home_screen_view_model.dart';
import 'package:get/get.dart';
import 'package:orange_ui/model/user/registration_user.dart';
import 'package:orange_ui/screen/user_detail_screen/user_detail_screen.dart';

class SaveScreenViewModel extends BaseViewModel {
  final SavedProfilesService _savedProfilesService = SavedProfilesService();

  List<DatingProfile> get savedProfiles => _savedProfilesService.savedProfiles;

  void init() {
    _savedProfilesService.addListener(notifyListeners);
  }

  @override
  void dispose() {
    _savedProfilesService.removeListener(notifyListeners);
    super.dispose();
  }

  void onProfileTap(DatingProfile profile) {
    // Navigate to user detail screen with minimal userData
    print('Profile tapped: ${profile.name}');
    Get.to(() => UserDetailScreen());
  }

  void onRemoveProfile(DatingProfile profile) {
    _savedProfilesService.removeProfile(profile);
    print('Removed ${profile.name} from saved profiles');
  }
}
