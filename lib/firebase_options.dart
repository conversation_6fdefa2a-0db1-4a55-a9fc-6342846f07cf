// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAciRBnvNFWT4lTqSpxupuz2jX5GhCrVB4',
    appId: '1:734253388961:android:9e85e6376013e951970ce0',
    messagingSenderId: '734253388961',
    projectId: 'nurseminglelove-0l405h',
    storageBucket: 'nurseminglelove-0l405h.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCdAe-BYPMNUXl7KFnx23YhSjRQpAUFJww',
    appId: '1:734253388961:ios:5a9a2a861aef0741970ce0',
    messagingSenderId: '734253388961',
    projectId: 'nurseminglelove-0l405h',
    storageBucket: 'nurseminglelove-0l405h.firebasestorage.app',
    androidClientId: '734253388961-5cvdpuu73bmi7ugsu8hj9q1bss6ua01n.apps.googleusercontent.com',
    iosClientId: '734253388961-c96jroe2njjf5g8g5r02bos2tp2d3do1.apps.googleusercontent.com',
    iosBundleId: 'com.mycompany.nurseminglelove',
  );
}
