import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:orange_ui/model/search/search_filter.dart';
import 'package:orange_ui/service/pref_service.dart';
import 'package:orange_ui/utils/pref_res.dart';

class FilterService {
  static FilterService? _instance;
  static FilterService get instance => _instance ??= FilterService._();
  FilterService._();

  SearchFilter _currentFilter = SearchFilter();

  SearchFilter get currentFilter => _currentFilter;

  // Load saved filters from preferences
  Future<void> loadFilters() async {
    try {
      String? filterJson = await PrefService.getString(PrefConst.searchFilters);
      if (filterJson != null && filterJson.isNotEmpty) {
        Map<String, dynamic> filterMap = jsonDecode(filterJson);
        _currentFilter = SearchFilter.fromJson(filterMap);
      }
    } catch (e) {
      print('Error loading filters: $e');
      _currentFilter = SearchFilter(); // Reset to default
    }
  }

  // Save filters to preferences
  Future<void> saveFilters(SearchFilter filter) async {
    try {
      _currentFilter = filter;
      String filterJson = jsonEncode(filter.toJson());
      await PrefService.saveString(PrefConst.searchFilters, filterJson);
    } catch (e) {
      print('Error saving filters: $e');
    }
  }

  // Update specific filter properties
  Future<void> updateGenderPreference(String gender) async {
    SearchFilter updatedFilter = _currentFilter.copyWith(
      genderPreference: gender,
    );
    await saveFilters(updatedFilter);
  }

  Future<void> updateDistance(double distance, String unit) async {
    SearchFilter updatedFilter = _currentFilter.copyWith(
      distanceKm: unit == 'miles' ? distance * 1.60934 : distance,
      distanceUnit: unit,
    );
    await saveFilters(updatedFilter);
  }

  Future<void> updateRegion(String? region, String? country) async {
    SearchFilter updatedFilter = _currentFilter.copyWith(
      selectedRegion: region,
      selectedCountry: country,
    );
    await saveFilters(updatedFilter);
  }

  Future<void> updateAgeRange(double start, double end) async {
    SearchFilter updatedFilter = _currentFilter.copyWith(
      ageRange: RangeValues(start, end),
    );
    await saveFilters(updatedFilter);
  }

  Future<void> updateInterests(List<String> interests) async {
    SearchFilter updatedFilter = _currentFilter.copyWith(
      selectedInterests: interests,
    );
    await saveFilters(updatedFilter);
  }

  // Reset filters to default
  Future<void> resetFilters() async {
    await saveFilters(SearchFilter());
  }

  // Get available regions and countries
  List<RegionData> getRegionsData() {
    return [
      RegionData(
        name: 'North America',
        countries: ['United States', 'Canada', 'Mexico'],
      ),
      RegionData(
        name: 'Europe',
        countries: [
          'United Kingdom',
          'Germany',
          'France',
          'Italy',
          'Spain',
          'Netherlands',
          'Sweden',
          'Norway',
          'Denmark',
          'Finland',
          'Poland',
          'Czech Republic',
        ],
      ),
      RegionData(
        name: 'Asia',
        countries: [
          'India',
          'China',
          'Japan',
          'South Korea',
          'Thailand',
          'Singapore',
          'Malaysia',
          'Indonesia',
          'Philippines',
          'Vietnam',
          'Pakistan',
          'Bangladesh',
        ],
      ),
      RegionData(
        name: 'Africa',
        countries: [
          'South Africa',
          'Nigeria',
          'Kenya',
          'Egypt',
          'Morocco',
          'Ghana',
          'Ethiopia',
          'Tanzania',
          'Uganda',
          'Zimbabwe',
        ],
      ),
      RegionData(
        name: 'South America',
        countries: [
          'Brazil',
          'Argentina',
          'Chile',
          'Colombia',
          'Peru',
          'Venezuela',
          'Ecuador',
          'Uruguay',
          'Paraguay',
          'Bolivia',
        ],
      ),
      RegionData(
        name: 'Oceania',
        countries: ['Australia', 'New Zealand', 'Fiji', 'Papua New Guinea'],
      ),
    ];
  }

  // Get available interests/hobbies
  List<String> getAvailableInterests() {
    return [
      'Travel',
      'Music',
      'Movies',
      'Sports',
      'Fitness',
      'Cooking',
      'Reading',
      'Art',
      'Photography',
      'Dancing',
      'Gaming',
      'Technology',
      'Fashion',
      'Nature',
      'Hiking',
      'Swimming',
      'Yoga',
      'Meditation',
      'Coffee',
      'Wine',
      'Food',
      'Shopping',
      'Concerts',
      'Theater',
      'Museums',
      'Beach',
      'Skiing',
      'Cycling',
      'Running',
      'Climbing',
      'Surfing',
      'Camping',
      'Fishing',
      'Gardening',
      'Pets',
      'Volunteering',
      'Learning',
      'Writing',
      'Blogging',
    ];
  }

  // Convert distance based on unit preference
  double getDisplayDistance() {
    if (_currentFilter.distanceUnit == 'miles') {
      return _currentFilter.distanceKm / 1.60934;
    }
    return _currentFilter.distanceKm;
  }

  // Get formatted distance string
  String getFormattedDistance() {
    double distance = getDisplayDistance();
    return '${distance.round()} ${_currentFilter.distanceUnit}';
  }
}
