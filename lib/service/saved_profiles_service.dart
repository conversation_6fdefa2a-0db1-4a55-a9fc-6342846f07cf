import 'package:flutter/foundation.dart';
import 'package:orange_ui/screen/home_screen/home_screen_view_model.dart';

class SavedProfilesService extends ChangeNotifier {
  static final SavedProfilesService _instance = SavedProfilesService._internal();

  factory SavedProfilesService() {
    return _instance;
  }

  SavedProfilesService._internal();

  final List<DatingProfile> _savedProfiles = [];

  List<DatingProfile> get savedProfiles => List.unmodifiable(_savedProfiles);

  void addProfile(DatingProfile profile) {
    if (!_savedProfiles.any((p) => p.id == profile.id)) {
      _savedProfiles.insert(0, profile);
      notifyListeners();
    }
  }

  void removeProfile(DatingProfile profile) {
    _savedProfiles.removeWhere((p) => p.id == profile.id);
    notifyListeners();
  }

  bool isProfileSaved(int profileId) {
    return _savedProfiles.any((p) => p.id == profileId);
  }
}
