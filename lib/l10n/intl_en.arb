{"@@locale": "en", "getStarted1Subtitle": "Find: Call: Chat: Connect", "continueText": "Continue", "fitness": "Fitness", "music": "Music", "foodies": "Foodies", "movies": "Movies", "walking": "Walking", "chef": "Chef", "singing": "Singing", "travel": "Travel", "artist": "Artist", "exploreProfiles": "Explore Profiles", "getStarted2Subtitle": "Craft your profile based on \ndifferent interests and find \nlike minded people", "thatGreat": "That's Great", "skip": "<PERSON><PERSON>", "nearbyProfileOnMap": "Nearby Profiles on Map", "getStarted3Subtitle": "Allow access to get your location \nand find local people nearby \nand connect with them.", "allow": "Allow", "connectThroughCalls": "Connect Through Calls", "getStarted4Subtitle": "Make high-quality video and audio calls \nwith people around the world,\n chat and share your moments together", "loginToContinue": "LOG IN TO CONTINUE", "email": "Email", "enterEmail": "Enter The Email", "validEmail": "<PERSON><PERSON> <PERSON><PERSON>", "or": "Or", "continueWithGoogle": "Continue With Google", "continueWithFacebook": "Continue With Facebook", "continueWithApple": "Continue With Apple", "donTHaveAnAccount": "Don't have an account?", "signUp": "Sign Up", "forgotYourPassword": "Forgot Your Password ?", "logIn": "LOG IN", "password": "Password", "enterPassword": "Enter the password", "view": "View", "hide": "<PERSON>de", "register": "REGISTER", "registerInfoText": "Looks like you don't have an account. \nLet's create new account ", "fullName": "Full Name", "registrationSuccessfully": "Registration Successfully done", "pleaseValidEmail": "Please valid email", "enterFullName": "Enter Full Name", "confirmPassword": "Confirm Password", "enterConfirmPassword": "Enter Confirm Password", "passwordMismatch": "Password Mismatch", "policy1": "By selecting Agree and continue below, \n I agree to ", "policy2": "Terms of Use", "policy3": " and ", "policy4": "Privacy Policy", "agreeNContinue": "Agree & Continue", "startingProfileInfoText": "Craft your profile with amazing Photos, Interests Bio and stand out from others !", "profile": "Profile", "whereDoYouLive": "WHERE DO YOU LIVE ?", "enterAddress": "City, Country", "bio": "BIO", "userBlock": "User block by admin", "enterBio": "Enter BIO", "enterAbout": "Enter ABOUT", "enterAge": "Enter AGE", "age": "AGE", "gender": "GENDER", "optional": "Optional", "male": "Male", "female": "Female", "drivingLicence": "Driving Licence", "idCard": "National ID Card", "other": "Other", "next": "NEXT", "photosCap": "PHOTOS", "explore": "Explore", "randoms": "Randoms", "joinLive": "Lives", "message": "Message", "priceCap": "PRICE", "videoCall": "Video Call", "liveCap": "LIVE", "nowCap": "NOW", "reverse": "REVERSE", "empty": "EMPTY", "couldNotLaunch": "Could not launch", "itLooksLikeEtc": "It looks like your wallet has insufficient coins for this action. let’s recharge it to enjoy this feature.", "swipe": "SWIPE", "useAutomaticallyEtc": "use automatically from next", "walletCap": "WALLET", "pleaseSelectImage": "Please Select Image", "moreInfo": "MORE INFO", "hideInfo": "HIDE INFO", "reportUser": "REPORT USER", "join": "JOIN", "explainMore": "Explain More", "iAgreeTo": "I agree to", "continuePlease": " continue please", "youAreReporting": "Your Are Reporting this user", "termAndCondition": " Terms & Conditions,", "selectReason": "Select Reason", "userNotLive": "User not live", "cyberbullying": "Cyberbullying", "harassment": "Harassment", "personalHarassment": "Personal Harassment", "inappropriateContent": "Inappropriate Content", "about": "About", "enterFullReason": "Enter Full Reason", "chatWith": "CHAT WITH ", "share": "SHARE", "reportCap": "REPORT ", "notification": "NOTIFICATIONS", "personal": "Personal", "platform": "Platform", "go": "Go", "continueCap": "CONTINUE", "terms": "Terms", "and": "and", "privacyPolicy": "Privacy Policy", "today": "Today", "pleaseCheckTerm": "Please check Term & Condition", "block": "Block", "image": "IMAGE", "selected": "Selected", "videoCap": "VIDEO", "videos": "Videos", "unblockCap": "UNBLOCK", "youBlockThisUser": "You block this user", "toSendMessage": "to send a message.", "close": "Close", "sendMedia": "Send Media", "send": "Send", "you": "you", "photos": "Photos", "yesterday": "Yesterday", "thisUserBlockYou": "This User Block You", "whichItemWouldYouLikeEtc": "Which item would you like to select?\nSelect a item", "writeMessage": "Write Message", "unBlock": "Unblock", "report": "Report", "cancel": "Cancel", "deleteMessage": "Delete message", "deleteThisChat": "Delete this chat", "areYouSureYouEtc": "Are you sure you want to delete this message ?", "messageWillOnlyBeRemoved": "Message will only be removed from this device Are you sure?", "areYouSure": "Are you sure", "chatHint": "Type something...!", "yes": "Yes", "no": "No", "map": "Map", "km": "Km", "findSomeoneRandomly": "Find Someone Randomly\nAnd check their profile ", "boys": "BOYS", "areYou": "Are you", "sure": " Sure?", "both": "BOTH", "girls": "GIRLS", "startMatching": "START MATCHING", "ok": "Ok", "searching": "Searching...", "cancelCap": "CANCEL", "deleteCap": "DELETE", "delete": "Delete", "deleteChat": "Delete Chat", "interest": "Interest", "editProfile": "EDIT PROFILE", "edit": "EDIT", "profileCap": "PROFILE", "fullNameCap": "FULL NAME", "docType": "DOCUMENT TYPE", "yourSelfie": "YOUR SELFIE", "instagram": "INSTAGRAM", "facebook": "FACEBOOK", "youtube": "YOUTUBE", "save": "SAVE", "pleaseAddAtLeastEtc": "Please add at least 1 image", "pleaseAddAtLeastInterest": "Please add at least 1 interest", "imageIsEmpty": "Image is Empty", "options": "OPTIONS", "reqVerification": "REQUEST VERIFICATION", "livestream": "Wallet / Call Dashboard", "verification": "Apply for Verification", "selectDocument": "Select Document", "takePhoto": "Take Photo", "pleaseAddSelfiePhoto": "Please add selfie photo", "verifiedAccountsHaveBlueEtc": "Verified accounts have blue checkmarks next to their names to show that we have confirmed they are the real presence of the public figures or celebrities.", "privacy": "PRIVACY SETTINGS", "pushNotification": "Push Notifications", "notificationData": "Keep it on, if you want to receive notifications", "switchMap": "Show Me On Map", "switchMapData": "Keep it on, if you want to be seen on Map", "anonymous": "Go Annonymous", "anonymousData": "Turn On, if you don't want to be seen anywhere in the app.Like: Search,Card Stack.", "subscription": "Subscription", "subscriptionSettings": "Subscription Settings", "manageSubscription": "Manage your subscription and billing", "changePassword": "Change Password", "changePasswordDesc": "Update your account password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "passwordChanged": "Password changed successfully", "incorrectCurrentPassword": "Current password is incorrect", "passwordsDoNotMatch": "New passwords do not match", "enterCurrentPassword": "Enter current password", "enterNewPassword": "Enter new password", "enterConfirmNewPassword": "Confirm new password", "updatePassword": "UPDATE PASSWORD", "accountSettings": "ACCOUNT SETTINGS", "termsOfUse": "Terms Of Use", "logOut": "Log Out", "deleteAccount": "Delete Account", "versionText": "Version 1.0.0", "legal": "LEGAL", "deleteDialogDis": "Do you really want to delete your account? You won't be able to recover it later and data will be lost forever", "logOutDis": "Do you really want to logout from this app?", "liveStreamCap": "CALL", "streamCap": "CALL", "areYouSureYouWantToEnd": "Are you sure you want to end your call?", "doYouReallyWantToLive": "Do you really want to make a call. Please continue to start calling", "videoPreview": "Video Preview Screen", "failedToLoadVideo": "Failed to load video: Cannot Open", "application": " APPLICATION", "something": "SOMETHING ABOUT YOU?", "shortIntro": "Short intro about you..", "languages": "Languages", "appLanguages": "App Languages", "languagesYouEtc": "LANGUAGES YOU WILL SPEAK", "languagesDetail": "Languages you will speak during calls..", "intro": "SHORT INTRO VIDEO", "attach": "Attach", "social": "SOCIAL PROFILE LINKS", "socialData": "Links to some of your social media profiles,which helps us to know more about your fan followings.", "submit": "SUBMIT", "history": " HISTORY", "time": " Time:", "streamed": " Called For:", "diamond": " Diamonds Collected:", "live": "Live", "goLive": "MAKE CALL", "failedPayment": "Failed payment", "end": "END", "viewers": "Viewers", "collected": "Collected", "comment": "Comment...", "exit": "Exit", "youWillBeSentEtc": "YOU WILL BE SENT OUTSIDE SOON", "subscribeToProEtc": "Subscribe to PRO - Have No Limits", "pro": "PRO", "add": "ADD", "diamonds": "DIAMONDS", "diamondsCamel": "Diamonds", "redeem": "REDEEM", "requests": " REQUESTS", "complete": "Complete", "processing": "Processing", "diamond1": "Diamonds:", "amount": "Amount Paid:", "noRedeemData": "No redeem data", "dashboard": " DASHBOARD", "getAccess": "GET ACCESS TO MAKE CALLS", "liveVerification": "CALL VERIFICATION", "apply": "APPLY", "eligibility": "Call Eligibility", "eligible": "ELIGIBLE", "pending": "PENDING", "notEligible": "NOT ELIGIBLE", "wallet": "DIAMOND WALLET", "liveCAp": "Live", "threshold": "Minimum Threshold : ", "redeemCap": "REDEEM", "addCoins": "ADD COINS", "totalStream": "TOTAL CALLS", "totalCollection": "TOTAL COLLECTION", "redeemRequests": "REDEEM REQUESTS", "diamondCap": "DIAMOND", "shop": "SHOP", "bayContinuingThePurchaseEtc": "By continuing the purchase you agree to our ", "automatically": "automatically", "paypal": "PayPal", "enterAccountDetails": "Enter Account details", "bankTransfer": "Bank Transfer", "noDataAvailable": "No Data Available", "youMustBe18": "You must be 18+", "accept": "Accept", "noUsersAreLive": "No users are live", "pleaseApplyForLiveStreamFromLivestreamDashboardFromProfile": "Please apply for call access from call dashboard from profile!", "yourApplicationIsPendingPleaseWait": "Your Application is pending please wait", "change": "Change", "tooLarge": "Too Large", "video": "Video?", "thisVideoIsGreaterThan50MbnpleaseSelectAnother": "This video is greater than 50 mb\nPlease select another...", "selectAnother": "Select another", "pleaseAddSocialLinks": "Please add social links", "yourLiveStreamHasBeenEndednbelowIsASummaryOf": "Your call has been ended!\nBelow is a summary of it.", "streamFor": "Call duration", "users": "Users", "pleaseEnterEmail": "Please Enter Email...!", "pleaseEnterValidEmailAddress": "Please Enter valid email address", "emailSentSuccessfully": "Email sent Successfully...", "forgotPassword": "Forgot Password?", "enterYourMailOnWhichYouHaveNcreatedAnAccount": "Enter your mail on which you have \ncreated an account. We will send a link \nto reset your password", "reset": "Reset", "enterThePasswordForTheAccountNwithTheEmailBelow": "Enter the password for the account \nwith the email below", "incorrectPasswordOrUserid": "Incorrect password or UserId", "pleaseVerifyYourEmailFromYourInbox": "Please Verify your e-mail from your inbox", "noData": "No Data", "hasLikedYourProfileYouShouldCheckTheirProfile": " has liked your profile, you should check their profile!", "youAreFakeUser": "You are fake User", "youMust18": "You Must 18+", "paymentGateway": "PAYMENT GATEWAY", "accountDetails": "ACCOUNT DETAILS", "noLocation": "No Location", "kmsAway": "kms Away", "checkOutThisProfile": "Check out this Profile", "look": "Look", "reverseSwipeWillCostYou": "Reverse Swipe will cost you", "coinsPleaseConfirmIfYouToContinueOrNot": "coins, Please confirm if you to continue or not", "messagePriceWillCostYou": "Message price will cost you", "coinsPerMsgPleaseConfirmIfYouToContinueOr": "coins per Msg, Please confirm if you to continue or not", "liveStreamPriceWillCostYou": "Call price will cost you", "coinsPerMinutesPleaseConfirmIfYouToContinueOr": "coins per minutes, Please confirm if you to continue or not", "liveStreamEnded": "Call Ended", "savedProfiles": "Saved Profiles", "likeProfiles": "Like Profiles", "blockedProfiles": "Blocked Profiles", "lives": "Calls", "feed": "Feed", "comments": "Comments", "addComment": "Add Comment", "photo": "Photo", "writeSomethingHere": "Write something here...", "createPost": "Create Post", "post": "Post", "selectInterestsToContinue": "Select interests to continue", "posts": "Posts", "follow": "Follow", "followers": "Followers", "following": "Following", "followingList": "Following List", "searchProfile": "Search Profile", "unfollow": "Unfollow", "commentNotFound": "Comment Not Found", "noComment": "No Comment", "reportedSubmitted": "Reported Submitted", "reportedSuccessfully": "Reported Successfully!!", "userNotFound": "User Not Found!!", "doYouReallyWantToDeleteThisChatYouWont": "Do you really want to delete this chat You won’t be able to recover any kind of data after. Hope you are aware of that!", "afterDeletingTheChatYouCanNotRestoreOurMessage": "After deleting the chat, you can not restore our message. Message will be deleted from your account.", "areYouSureYouWantToDeleteThePost": "Are you sure you want to delete the post?", "deletePost": "Delete post", "reportPost": "Report post", "doYouWantToDeleteThisStoryYouCanNot": "Do you want to delete this story?, You can not restore the story it will be permanently deleted.", "deleteThisStory": "Delete this story?", "pleaseEnterYourAge": "Please enter your age", "noLikeData": "No Like Data", "noSavedData": "No Saved Data", "whatDoYouWantToSelect": "What do you want to select?", "readLess": "Read Less...", "readMore": "Read More...", "videoDurationIs": "Video duration is", "large": "Large", "commentDelete": "Comment delete ?", "areYouSureYouWantToDeleteTheComment": "Are you sure you want to delete the comment ?", "enterUsername": "Enter UserName", "username": "UserName", "followerList": "Follower List", "userDidNotAllowCameraAndMicrophonePermission": "User did not allow camera and microphone permission.", "userDidNotAllowCamera": "User did not allow camera", "openSettings": "Open Settings", "ifAppearsThatCameraPermissionHasNotBeenGrantedTo": "If appears that camera permission has not been granted. To the App, you will need to allow access to the camera from the settings.", "toAccessYourCameraAndMicrophone": "to access your camera and microphone", "pleaseSelectYourInterestsOrSkipThisStep": "Please select your interests, or skip this step.", "previous": "Previous", "swipeRightToViewTheNextProfile": "Swipe right to view the next profile.", "genderPref": "Gender Preference", "agePref": "Age Preference"}