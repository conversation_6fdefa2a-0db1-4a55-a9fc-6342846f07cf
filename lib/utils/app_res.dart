import 'package:orange_ui/generated/l10n.dart';

class AppRes {
  // AppName
  static const String appName = 'Orange';

  // For Datetime
  static const String hmmA = "h:mm a";
  static const String hhMmA = "hh:mm a";
  static const String dMY = "dd MMM yyyy";
  static const String isHttp = "http://";
  static const String isHttps = "https://";

  // For Dialog
  static String reverseSwipeDisc(int reverseSwipePrice) {
    return "${S.current.reverseSwipeWillCostYou} $reverseSwipePrice ${S.current.coinsPleaseConfirmIfYouToContinueOrNot}";
  }

  static String messageDisc(int? messagePrice) {
    return "${S.current.messagePriceWillCostYou} ${messagePrice ?? 0} ${S.current.coinsPerMsgPleaseConfirmIfYouToContinueOr}";
  }

  static const String report = 'Report';

  static const String defaultFullname = 'Unknown';

  static const String male = 'Male';
  static const String female = 'Female';
  static const String other = 'Other';

  static String videoDurationDescription(int second) {
    return 'This video is more then $second seconds.  Please select another...';
  }

  static String planName = 'Diamond';
  static String defaultCurrencyCode = '\$';

  ///------------------------ Notification Topic ------------------------///
  static const String subscribeTopic = 'orange';

  ///------------------------ Chat  ------------------------///

  static const int maxVideoUploadSize = 15; // Enter in MB

  // Image picker quality
  static const double maxWidth = 720;
  static const double maxHeight = 720;
  static const int quality = 100;

  // Max Images for Post
  static const int defaultMaxImagesForPost = 5;

  // Pagination data limit
  static const int paginationLimit = 15;

  // Age Limit
  static const double ageMin = 0;
  static const double ageMax = 100;

  // default genderPref
  static const defaultGenderPref = 1;

  // Change Diamond Icon
  static const coinIcon = '💎';
}
