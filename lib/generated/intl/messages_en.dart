// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about": MessageLookupByLibrary.simpleMessage("About"),
    "accept": MessageLookupByLibrary.simpleMessage("Accept"),
    "accountDetails": MessageLookupByLibrary.simpleMessage("ACCOUNT DETAILS"),
    "accountSettings": MessageLookupByLibrary.simpleMessage("ACCOUNT SETTINGS"),
    "add": MessageLookupByLibrary.simpleMessage("ADD"),
    "addCoins": MessageLookupByLibrary.simpleMessage("ADD COINS"),
    "addComment": MessageLookupByLibrary.simpleMessage("Add Comment"),
    "afterDeletingTheChatYouCanNotRestoreOurMessage":
        MessageLookupByLibrary.simpleMessage(
          "After deleting the chat, you can not restore our message. Message will be deleted from your account.",
        ),
    "age": MessageLookupByLibrary.simpleMessage("AGE"),
    "agePref": MessageLookupByLibrary.simpleMessage("Age Preference"),
    "agreeNContinue": MessageLookupByLibrary.simpleMessage("Agree & Continue"),
    "allow": MessageLookupByLibrary.simpleMessage("Allow"),
    "amount": MessageLookupByLibrary.simpleMessage("Amount Paid:"),
    "and": MessageLookupByLibrary.simpleMessage("and"),
    "anonymous": MessageLookupByLibrary.simpleMessage("Go Annonymous"),
    "anonymousData": MessageLookupByLibrary.simpleMessage(
      "Turn On, if you don\'t want to be seen anywhere in the app.Like: Search,Card Stack.",
    ),
    "appLanguages": MessageLookupByLibrary.simpleMessage("App Languages"),
    "application": MessageLookupByLibrary.simpleMessage(" APPLICATION"),
    "apply": MessageLookupByLibrary.simpleMessage("APPLY"),
    "areYou": MessageLookupByLibrary.simpleMessage("Are you"),
    "areYouSure": MessageLookupByLibrary.simpleMessage("Are you sure"),
    "areYouSureYouEtc": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this message ?",
    ),
    "areYouSureYouWantToDeleteTheComment": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete the comment ?",
    ),
    "areYouSureYouWantToDeleteThePost": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete the post?",
    ),
    "areYouSureYouWantToEnd": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to end your call?",
    ),
    "artist": MessageLookupByLibrary.simpleMessage("Artist"),
    "attach": MessageLookupByLibrary.simpleMessage("Attach"),
    "automatically": MessageLookupByLibrary.simpleMessage("automatically"),
    "bankTransfer": MessageLookupByLibrary.simpleMessage("Bank Transfer"),
    "bayContinuingThePurchaseEtc": MessageLookupByLibrary.simpleMessage(
      "By continuing the purchase you agree to our ",
    ),
    "bio": MessageLookupByLibrary.simpleMessage("BIO"),
    "block": MessageLookupByLibrary.simpleMessage("Block"),
    "blockedProfiles": MessageLookupByLibrary.simpleMessage("Blocked Profiles"),
    "both": MessageLookupByLibrary.simpleMessage("BOTH"),
    "boys": MessageLookupByLibrary.simpleMessage("BOYS"),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cancelCap": MessageLookupByLibrary.simpleMessage("CANCEL"),
    "change": MessageLookupByLibrary.simpleMessage("Change"),
    "changePassword": MessageLookupByLibrary.simpleMessage("Change Password"),
    "changePasswordDesc": MessageLookupByLibrary.simpleMessage(
      "Update your account password",
    ),
    "chatHint": MessageLookupByLibrary.simpleMessage("Type something...!"),
    "chatWith": MessageLookupByLibrary.simpleMessage("CHAT WITH "),
    "checkOutThisProfile": MessageLookupByLibrary.simpleMessage(
      "Check out this Profile",
    ),
    "chef": MessageLookupByLibrary.simpleMessage("Chef"),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "coinsPerMinutesPleaseConfirmIfYouToContinueOr":
        MessageLookupByLibrary.simpleMessage(
          "coins per minutes, Please confirm if you to continue or not",
        ),
    "coinsPerMsgPleaseConfirmIfYouToContinueOr":
        MessageLookupByLibrary.simpleMessage(
          "coins per Msg, Please confirm if you to continue or not",
        ),
    "coinsPleaseConfirmIfYouToContinueOrNot":
        MessageLookupByLibrary.simpleMessage(
          "coins, Please confirm if you to continue or not",
        ),
    "collected": MessageLookupByLibrary.simpleMessage("Collected"),
    "comment": MessageLookupByLibrary.simpleMessage("Comment..."),
    "commentDelete": MessageLookupByLibrary.simpleMessage("Comment delete ?"),
    "commentNotFound": MessageLookupByLibrary.simpleMessage(
      "Comment Not Found",
    ),
    "comments": MessageLookupByLibrary.simpleMessage("Comments"),
    "complete": MessageLookupByLibrary.simpleMessage("Complete"),
    "confirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "Confirm New Password",
    ),
    "confirmPassword": MessageLookupByLibrary.simpleMessage("Confirm Password"),
    "connectThroughCalls": MessageLookupByLibrary.simpleMessage(
      "Connect Through Calls",
    ),
    "continueCap": MessageLookupByLibrary.simpleMessage("CONTINUE"),
    "continuePlease": MessageLookupByLibrary.simpleMessage(" continue please"),
    "continueText": MessageLookupByLibrary.simpleMessage("Continue"),
    "continueWithApple": MessageLookupByLibrary.simpleMessage(
      "Continue With Apple",
    ),
    "continueWithFacebook": MessageLookupByLibrary.simpleMessage(
      "Continue With Facebook",
    ),
    "continueWithGoogle": MessageLookupByLibrary.simpleMessage(
      "Continue With Google",
    ),
    "couldNotLaunch": MessageLookupByLibrary.simpleMessage("Could not launch"),
    "createPost": MessageLookupByLibrary.simpleMessage("Create Post"),
    "currentPassword": MessageLookupByLibrary.simpleMessage("Current Password"),
    "cyberbullying": MessageLookupByLibrary.simpleMessage("Cyberbullying"),
    "dashboard": MessageLookupByLibrary.simpleMessage(" DASHBOARD"),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "deleteAccount": MessageLookupByLibrary.simpleMessage("Delete Account"),
    "deleteCap": MessageLookupByLibrary.simpleMessage("DELETE"),
    "deleteChat": MessageLookupByLibrary.simpleMessage("Delete Chat"),
    "deleteDialogDis": MessageLookupByLibrary.simpleMessage(
      "Do you really want to delete your account? You won\'t be able to recover it later and data will be lost forever",
    ),
    "deleteMessage": MessageLookupByLibrary.simpleMessage("Delete message"),
    "deletePost": MessageLookupByLibrary.simpleMessage("Delete post"),
    "deleteThisChat": MessageLookupByLibrary.simpleMessage("Delete this chat"),
    "deleteThisStory": MessageLookupByLibrary.simpleMessage(
      "Delete this story?",
    ),
    "diamond": MessageLookupByLibrary.simpleMessage(" Diamonds Collected:"),
    "diamond1": MessageLookupByLibrary.simpleMessage("Diamonds:"),
    "diamondCap": MessageLookupByLibrary.simpleMessage("DIAMOND"),
    "diamonds": MessageLookupByLibrary.simpleMessage("DIAMONDS"),
    "diamondsCamel": MessageLookupByLibrary.simpleMessage("Diamonds"),
    "doYouReallyWantToDeleteThisChatYouWont": MessageLookupByLibrary.simpleMessage(
      "Do you really want to delete this chat You won’t be able to recover any kind of data after. Hope you are aware of that!",
    ),
    "doYouReallyWantToLive": MessageLookupByLibrary.simpleMessage(
      "Do you really want to make a call. Please continue to start calling",
    ),
    "doYouWantToDeleteThisStoryYouCanNot": MessageLookupByLibrary.simpleMessage(
      "Do you want to delete this story?, You can not restore the story it will be permanently deleted.",
    ),
    "docType": MessageLookupByLibrary.simpleMessage("DOCUMENT TYPE"),
    "donTHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "Don\'t have an account?",
    ),
    "drivingLicence": MessageLookupByLibrary.simpleMessage("Driving Licence"),
    "edit": MessageLookupByLibrary.simpleMessage("EDIT"),
    "editProfile": MessageLookupByLibrary.simpleMessage("EDIT PROFILE"),
    "eligibility": MessageLookupByLibrary.simpleMessage("Call Eligibility"),
    "eligible": MessageLookupByLibrary.simpleMessage("ELIGIBLE"),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "emailSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Email sent Successfully...",
    ),
    "empty": MessageLookupByLibrary.simpleMessage("EMPTY"),
    "end": MessageLookupByLibrary.simpleMessage("END"),
    "enterAbout": MessageLookupByLibrary.simpleMessage("Enter ABOUT"),
    "enterAccountDetails": MessageLookupByLibrary.simpleMessage(
      "Enter Account details",
    ),
    "enterAddress": MessageLookupByLibrary.simpleMessage("City, Country"),
    "enterAge": MessageLookupByLibrary.simpleMessage("Enter AGE"),
    "enterBio": MessageLookupByLibrary.simpleMessage("Enter BIO"),
    "enterConfirmNewPassword": MessageLookupByLibrary.simpleMessage(
      "Confirm new password",
    ),
    "enterConfirmPassword": MessageLookupByLibrary.simpleMessage(
      "Enter Confirm Password",
    ),
    "enterCurrentPassword": MessageLookupByLibrary.simpleMessage(
      "Enter current password",
    ),
    "enterEmail": MessageLookupByLibrary.simpleMessage("Enter The Email"),
    "enterFullName": MessageLookupByLibrary.simpleMessage("Enter Full Name"),
    "enterFullReason": MessageLookupByLibrary.simpleMessage(
      "Enter Full Reason",
    ),
    "enterNewPassword": MessageLookupByLibrary.simpleMessage(
      "Enter new password",
    ),
    "enterPassword": MessageLookupByLibrary.simpleMessage("Enter the password"),
    "enterThePasswordForTheAccountNwithTheEmailBelow":
        MessageLookupByLibrary.simpleMessage(
          "Enter the password for the account \nwith the email below",
        ),
    "enterUsername": MessageLookupByLibrary.simpleMessage("Enter UserName"),
    "enterYourMailOnWhichYouHaveNcreatedAnAccount":
        MessageLookupByLibrary.simpleMessage(
          "Enter your mail on which you have \ncreated an account. We will send a link \nto reset your password",
        ),
    "exit": MessageLookupByLibrary.simpleMessage("Exit"),
    "explainMore": MessageLookupByLibrary.simpleMessage("Explain More"),
    "explore": MessageLookupByLibrary.simpleMessage("Explore"),
    "exploreProfiles": MessageLookupByLibrary.simpleMessage("Explore Profiles"),
    "facebook": MessageLookupByLibrary.simpleMessage("FACEBOOK"),
    "failedPayment": MessageLookupByLibrary.simpleMessage("Failed payment"),
    "failedToLoadVideo": MessageLookupByLibrary.simpleMessage(
      "Failed to load video: Cannot Open",
    ),
    "feed": MessageLookupByLibrary.simpleMessage("Feed"),
    "female": MessageLookupByLibrary.simpleMessage("Female"),
    "findSomeoneRandomly": MessageLookupByLibrary.simpleMessage(
      "Find Someone Randomly\nAnd check their profile ",
    ),
    "fitness": MessageLookupByLibrary.simpleMessage("Fitness"),
    "follow": MessageLookupByLibrary.simpleMessage("Follow"),
    "followerList": MessageLookupByLibrary.simpleMessage("Follower List"),
    "followers": MessageLookupByLibrary.simpleMessage("Followers"),
    "following": MessageLookupByLibrary.simpleMessage("Following"),
    "followingList": MessageLookupByLibrary.simpleMessage("Following List"),
    "foodies": MessageLookupByLibrary.simpleMessage("Foodies"),
    "forgotPassword": MessageLookupByLibrary.simpleMessage("Forgot Password?"),
    "forgotYourPassword": MessageLookupByLibrary.simpleMessage(
      "Forgot Your Password ?",
    ),
    "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
    "fullNameCap": MessageLookupByLibrary.simpleMessage("FULL NAME"),
    "gender": MessageLookupByLibrary.simpleMessage("GENDER"),
    "genderPref": MessageLookupByLibrary.simpleMessage("Gender Preference"),
    "getAccess": MessageLookupByLibrary.simpleMessage(
      "GET ACCESS TO MAKE CALLS",
    ),
    "getStarted1Subtitle": MessageLookupByLibrary.simpleMessage(
      "Find: Call: Chat: Connect",
    ),
    "getStarted2Subtitle": MessageLookupByLibrary.simpleMessage(
      "Craft your profile based on \ndifferent interests and find \nlike minded people",
    ),
    "getStarted3Subtitle": MessageLookupByLibrary.simpleMessage(
      "Allow access to get your location \nand find local people nearby \nand connect with them.",
    ),
    "getStarted4Subtitle": MessageLookupByLibrary.simpleMessage(
      "Make high-quality video and audio calls \nwith people around the world,\n chat and share your moments together",
    ),
    "girls": MessageLookupByLibrary.simpleMessage("GIRLS"),
    "go": MessageLookupByLibrary.simpleMessage("Go"),
    "goLive": MessageLookupByLibrary.simpleMessage("MAKE CALL"),
    "harassment": MessageLookupByLibrary.simpleMessage("Harassment"),
    "hasLikedYourProfileYouShouldCheckTheirProfile":
        MessageLookupByLibrary.simpleMessage(
          " has liked your profile, you should check their profile!",
        ),
    "hide": MessageLookupByLibrary.simpleMessage("Hide"),
    "hideInfo": MessageLookupByLibrary.simpleMessage("HIDE INFO"),
    "history": MessageLookupByLibrary.simpleMessage(" HISTORY"),
    "iAgreeTo": MessageLookupByLibrary.simpleMessage("I agree to"),
    "idCard": MessageLookupByLibrary.simpleMessage("National ID Card"),
    "ifAppearsThatCameraPermissionHasNotBeenGrantedTo":
        MessageLookupByLibrary.simpleMessage(
          "If appears that camera permission has not been granted. To the App, you will need to allow access to the camera from the settings.",
        ),
    "image": MessageLookupByLibrary.simpleMessage("IMAGE"),
    "imageIsEmpty": MessageLookupByLibrary.simpleMessage("Image is Empty"),
    "inappropriateContent": MessageLookupByLibrary.simpleMessage(
      "Inappropriate Content",
    ),
    "incorrectCurrentPassword": MessageLookupByLibrary.simpleMessage(
      "Current password is incorrect",
    ),
    "incorrectPasswordOrUserid": MessageLookupByLibrary.simpleMessage(
      "Incorrect password or UserId",
    ),
    "instagram": MessageLookupByLibrary.simpleMessage("INSTAGRAM"),
    "interest": MessageLookupByLibrary.simpleMessage("Interest"),
    "intro": MessageLookupByLibrary.simpleMessage("SHORT INTRO VIDEO"),
    "itLooksLikeEtc": MessageLookupByLibrary.simpleMessage(
      "It looks like your wallet has insufficient coins for this action. let’s recharge it to enjoy this feature.",
    ),
    "join": MessageLookupByLibrary.simpleMessage("JOIN"),
    "joinLive": MessageLookupByLibrary.simpleMessage("Lives"),
    "km": MessageLookupByLibrary.simpleMessage("Km"),
    "kmsAway": MessageLookupByLibrary.simpleMessage("kms Away"),
    "languages": MessageLookupByLibrary.simpleMessage("Languages"),
    "languagesDetail": MessageLookupByLibrary.simpleMessage(
      "Languages you will speak during calls..",
    ),
    "languagesYouEtc": MessageLookupByLibrary.simpleMessage(
      "LANGUAGES YOU WILL SPEAK",
    ),
    "large": MessageLookupByLibrary.simpleMessage("Large"),
    "legal": MessageLookupByLibrary.simpleMessage("LEGAL"),
    "likeProfiles": MessageLookupByLibrary.simpleMessage("Like Profiles"),
    "live": MessageLookupByLibrary.simpleMessage("Live"),
    "liveCAp": MessageLookupByLibrary.simpleMessage("Live"),
    "liveCap": MessageLookupByLibrary.simpleMessage("LIVE"),
    "liveStreamCap": MessageLookupByLibrary.simpleMessage("CALL"),
    "liveStreamEnded": MessageLookupByLibrary.simpleMessage("Call Ended"),
    "liveStreamPriceWillCostYou": MessageLookupByLibrary.simpleMessage(
      "Call price will cost you",
    ),
    "liveVerification": MessageLookupByLibrary.simpleMessage(
      "CALL VERIFICATION",
    ),
    "lives": MessageLookupByLibrary.simpleMessage("Calls"),
    "livestream": MessageLookupByLibrary.simpleMessage(
      "Wallet / Call Dashboard",
    ),
    "logIn": MessageLookupByLibrary.simpleMessage("LOG IN"),
    "logOut": MessageLookupByLibrary.simpleMessage("Log Out"),
    "logOutDis": MessageLookupByLibrary.simpleMessage(
      "Do you really want to logout from this app?",
    ),
    "loginToContinue": MessageLookupByLibrary.simpleMessage(
      "LOG IN TO CONTINUE",
    ),
    "look": MessageLookupByLibrary.simpleMessage("Look"),
    "male": MessageLookupByLibrary.simpleMessage("Male"),
    "manageSubscription": MessageLookupByLibrary.simpleMessage(
      "Manage your subscription and billing",
    ),
    "map": MessageLookupByLibrary.simpleMessage("Map"),
    "message": MessageLookupByLibrary.simpleMessage("Message"),
    "messagePriceWillCostYou": MessageLookupByLibrary.simpleMessage(
      "Message price will cost you",
    ),
    "messageWillOnlyBeRemoved": MessageLookupByLibrary.simpleMessage(
      "Message will only be removed from this device Are you sure?",
    ),
    "moreInfo": MessageLookupByLibrary.simpleMessage("MORE INFO"),
    "movies": MessageLookupByLibrary.simpleMessage("Movies"),
    "music": MessageLookupByLibrary.simpleMessage("Music"),
    "nearbyProfileOnMap": MessageLookupByLibrary.simpleMessage(
      "Nearby Profiles on Map",
    ),
    "newPassword": MessageLookupByLibrary.simpleMessage("New Password"),
    "next": MessageLookupByLibrary.simpleMessage("NEXT"),
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "noComment": MessageLookupByLibrary.simpleMessage("No Comment"),
    "noData": MessageLookupByLibrary.simpleMessage("No Data"),
    "noDataAvailable": MessageLookupByLibrary.simpleMessage(
      "No Data Available",
    ),
    "noLikeData": MessageLookupByLibrary.simpleMessage("No Like Data"),
    "noLocation": MessageLookupByLibrary.simpleMessage("No Location"),
    "noRedeemData": MessageLookupByLibrary.simpleMessage("No redeem data"),
    "noSavedData": MessageLookupByLibrary.simpleMessage("No Saved Data"),
    "noUsersAreLive": MessageLookupByLibrary.simpleMessage("No users are live"),
    "notEligible": MessageLookupByLibrary.simpleMessage("NOT ELIGIBLE"),
    "notification": MessageLookupByLibrary.simpleMessage("NOTIFICATIONS"),
    "notificationData": MessageLookupByLibrary.simpleMessage(
      "Keep it on, if you want to receive notifications",
    ),
    "nowCap": MessageLookupByLibrary.simpleMessage("NOW"),
    "ok": MessageLookupByLibrary.simpleMessage("Ok"),
    "openSettings": MessageLookupByLibrary.simpleMessage("Open Settings"),
    "optional": MessageLookupByLibrary.simpleMessage("Optional"),
    "options": MessageLookupByLibrary.simpleMessage("OPTIONS"),
    "or": MessageLookupByLibrary.simpleMessage("Or"),
    "other": MessageLookupByLibrary.simpleMessage("Other"),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "passwordChanged": MessageLookupByLibrary.simpleMessage(
      "Password changed successfully",
    ),
    "passwordMismatch": MessageLookupByLibrary.simpleMessage(
      "Password Mismatch",
    ),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "New passwords do not match",
    ),
    "paymentGateway": MessageLookupByLibrary.simpleMessage("PAYMENT GATEWAY"),
    "paypal": MessageLookupByLibrary.simpleMessage("PayPal"),
    "pending": MessageLookupByLibrary.simpleMessage("PENDING"),
    "personal": MessageLookupByLibrary.simpleMessage("Personal"),
    "personalHarassment": MessageLookupByLibrary.simpleMessage(
      "Personal Harassment",
    ),
    "photo": MessageLookupByLibrary.simpleMessage("Photo"),
    "photos": MessageLookupByLibrary.simpleMessage("Photos"),
    "photosCap": MessageLookupByLibrary.simpleMessage("PHOTOS"),
    "platform": MessageLookupByLibrary.simpleMessage("Platform"),
    "pleaseAddAtLeastEtc": MessageLookupByLibrary.simpleMessage(
      "Please add at least 1 image",
    ),
    "pleaseAddAtLeastInterest": MessageLookupByLibrary.simpleMessage(
      "Please add at least 1 interest",
    ),
    "pleaseAddSelfiePhoto": MessageLookupByLibrary.simpleMessage(
      "Please add selfie photo",
    ),
    "pleaseAddSocialLinks": MessageLookupByLibrary.simpleMessage(
      "Please add social links",
    ),
    "pleaseApplyForLiveStreamFromLivestreamDashboardFromProfile":
        MessageLookupByLibrary.simpleMessage(
          "Please apply for call access from call dashboard from profile!",
        ),
    "pleaseCheckTerm": MessageLookupByLibrary.simpleMessage(
      "Please check Term & Condition",
    ),
    "pleaseEnterEmail": MessageLookupByLibrary.simpleMessage(
      "Please Enter Email...!",
    ),
    "pleaseEnterValidEmailAddress": MessageLookupByLibrary.simpleMessage(
      "Please Enter valid email address",
    ),
    "pleaseEnterYourAge": MessageLookupByLibrary.simpleMessage(
      "Please enter your age",
    ),
    "pleaseSelectImage": MessageLookupByLibrary.simpleMessage(
      "Please Select Image",
    ),
    "pleaseSelectYourInterestsOrSkipThisStep":
        MessageLookupByLibrary.simpleMessage(
          "Please select your interests, or skip this step.",
        ),
    "pleaseValidEmail": MessageLookupByLibrary.simpleMessage(
      "Please valid email",
    ),
    "pleaseVerifyYourEmailFromYourInbox": MessageLookupByLibrary.simpleMessage(
      "Please Verify your e-mail from your inbox",
    ),
    "policy1": MessageLookupByLibrary.simpleMessage(
      "By selecting Agree and continue below, \n I agree to ",
    ),
    "policy2": MessageLookupByLibrary.simpleMessage("Terms of Use"),
    "policy3": MessageLookupByLibrary.simpleMessage(" and "),
    "policy4": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "post": MessageLookupByLibrary.simpleMessage("Post"),
    "posts": MessageLookupByLibrary.simpleMessage("Posts"),
    "previous": MessageLookupByLibrary.simpleMessage("Previous"),
    "priceCap": MessageLookupByLibrary.simpleMessage("PRICE"),
    "privacy": MessageLookupByLibrary.simpleMessage("PRIVACY SETTINGS"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "pro": MessageLookupByLibrary.simpleMessage("PRO"),
    "processing": MessageLookupByLibrary.simpleMessage("Processing"),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "profileCap": MessageLookupByLibrary.simpleMessage("PROFILE"),
    "pushNotification": MessageLookupByLibrary.simpleMessage(
      "Push Notifications",
    ),
    "randoms": MessageLookupByLibrary.simpleMessage("Randoms"),
    "readLess": MessageLookupByLibrary.simpleMessage("Read Less..."),
    "readMore": MessageLookupByLibrary.simpleMessage("Read More..."),
    "redeem": MessageLookupByLibrary.simpleMessage("REDEEM"),
    "redeemCap": MessageLookupByLibrary.simpleMessage("REDEEM"),
    "redeemRequests": MessageLookupByLibrary.simpleMessage("REDEEM REQUESTS"),
    "register": MessageLookupByLibrary.simpleMessage("REGISTER"),
    "registerInfoText": MessageLookupByLibrary.simpleMessage(
      "Looks like you don\'t have an account. \nLet\'s create new account ",
    ),
    "registrationSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Registration Successfully done",
    ),
    "report": MessageLookupByLibrary.simpleMessage("Report"),
    "reportCap": MessageLookupByLibrary.simpleMessage("REPORT "),
    "reportPost": MessageLookupByLibrary.simpleMessage("Report post"),
    "reportUser": MessageLookupByLibrary.simpleMessage("REPORT USER"),
    "reportedSubmitted": MessageLookupByLibrary.simpleMessage(
      "Reported Submitted",
    ),
    "reportedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Reported Successfully!!",
    ),
    "reqVerification": MessageLookupByLibrary.simpleMessage(
      "REQUEST VERIFICATION",
    ),
    "requests": MessageLookupByLibrary.simpleMessage(" REQUESTS"),
    "reset": MessageLookupByLibrary.simpleMessage("Reset"),
    "reverse": MessageLookupByLibrary.simpleMessage("REVERSE"),
    "reverseSwipeWillCostYou": MessageLookupByLibrary.simpleMessage(
      "Reverse Swipe will cost you",
    ),
    "save": MessageLookupByLibrary.simpleMessage("SAVE"),
    "savedProfiles": MessageLookupByLibrary.simpleMessage("Saved Profiles"),
    "searchProfile": MessageLookupByLibrary.simpleMessage("Search Profile"),
    "searching": MessageLookupByLibrary.simpleMessage("Searching..."),
    "selectAnother": MessageLookupByLibrary.simpleMessage("Select another"),
    "selectDocument": MessageLookupByLibrary.simpleMessage("Select Document"),
    "selectInterestsToContinue": MessageLookupByLibrary.simpleMessage(
      "Select interests to continue",
    ),
    "selectReason": MessageLookupByLibrary.simpleMessage("Select Reason"),
    "selected": MessageLookupByLibrary.simpleMessage("Selected"),
    "send": MessageLookupByLibrary.simpleMessage("Send"),
    "sendMedia": MessageLookupByLibrary.simpleMessage("Send Media"),
    "share": MessageLookupByLibrary.simpleMessage("SHARE"),
    "shop": MessageLookupByLibrary.simpleMessage("SHOP"),
    "shortIntro": MessageLookupByLibrary.simpleMessage(
      "Short intro about you..",
    ),
    "signUp": MessageLookupByLibrary.simpleMessage("Sign Up"),
    "singing": MessageLookupByLibrary.simpleMessage("Singing"),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "social": MessageLookupByLibrary.simpleMessage("SOCIAL PROFILE LINKS"),
    "socialData": MessageLookupByLibrary.simpleMessage(
      "Links to some of your social media profiles,which helps us to know more about your fan followings.",
    ),
    "something": MessageLookupByLibrary.simpleMessage("SOMETHING ABOUT YOU?"),
    "startMatching": MessageLookupByLibrary.simpleMessage("START MATCHING"),
    "startingProfileInfoText": MessageLookupByLibrary.simpleMessage(
      "Craft your profile with amazing Photos, Interests Bio and stand out from others !",
    ),
    "streamCap": MessageLookupByLibrary.simpleMessage("CALL"),
    "streamFor": MessageLookupByLibrary.simpleMessage("Call duration"),
    "streamed": MessageLookupByLibrary.simpleMessage(" Called For:"),
    "submit": MessageLookupByLibrary.simpleMessage("SUBMIT"),
    "subscribeToProEtc": MessageLookupByLibrary.simpleMessage(
      "Subscribe to PRO - Have No Limits",
    ),
    "subscription": MessageLookupByLibrary.simpleMessage("Subscription"),
    "subscriptionSettings": MessageLookupByLibrary.simpleMessage(
      "Subscription Settings",
    ),
    "sure": MessageLookupByLibrary.simpleMessage(" Sure?"),
    "swipe": MessageLookupByLibrary.simpleMessage("SWIPE"),
    "swipeRightToViewTheNextProfile": MessageLookupByLibrary.simpleMessage(
      "Swipe right to view the next profile.",
    ),
    "switchMap": MessageLookupByLibrary.simpleMessage("Show Me On Map"),
    "switchMapData": MessageLookupByLibrary.simpleMessage(
      "Keep it on, if you want to be seen on Map",
    ),
    "takePhoto": MessageLookupByLibrary.simpleMessage("Take Photo"),
    "termAndCondition": MessageLookupByLibrary.simpleMessage(
      " Terms & Conditions,",
    ),
    "terms": MessageLookupByLibrary.simpleMessage("Terms"),
    "termsOfUse": MessageLookupByLibrary.simpleMessage("Terms Of Use"),
    "thatGreat": MessageLookupByLibrary.simpleMessage("That\'s Great"),
    "thisUserBlockYou": MessageLookupByLibrary.simpleMessage(
      "This User Block You",
    ),
    "thisVideoIsGreaterThan50MbnpleaseSelectAnother":
        MessageLookupByLibrary.simpleMessage(
          "This video is greater than 50 mb\nPlease select another...",
        ),
    "threshold": MessageLookupByLibrary.simpleMessage("Minimum Threshold : "),
    "time": MessageLookupByLibrary.simpleMessage(" Time:"),
    "toAccessYourCameraAndMicrophone": MessageLookupByLibrary.simpleMessage(
      "to access your camera and microphone",
    ),
    "toSendMessage": MessageLookupByLibrary.simpleMessage("to send a message."),
    "today": MessageLookupByLibrary.simpleMessage("Today"),
    "tooLarge": MessageLookupByLibrary.simpleMessage("Too Large"),
    "totalCollection": MessageLookupByLibrary.simpleMessage("TOTAL COLLECTION"),
    "totalStream": MessageLookupByLibrary.simpleMessage("TOTAL CALLS"),
    "travel": MessageLookupByLibrary.simpleMessage("Travel"),
    "unBlock": MessageLookupByLibrary.simpleMessage("Unblock"),
    "unblockCap": MessageLookupByLibrary.simpleMessage("UNBLOCK"),
    "unfollow": MessageLookupByLibrary.simpleMessage("Unfollow"),
    "updatePassword": MessageLookupByLibrary.simpleMessage("UPDATE PASSWORD"),
    "useAutomaticallyEtc": MessageLookupByLibrary.simpleMessage(
      "use automatically from next",
    ),
    "userBlock": MessageLookupByLibrary.simpleMessage("User block by admin"),
    "userDidNotAllowCamera": MessageLookupByLibrary.simpleMessage(
      "User did not allow camera",
    ),
    "userDidNotAllowCameraAndMicrophonePermission":
        MessageLookupByLibrary.simpleMessage(
          "User did not allow camera and microphone permission.",
        ),
    "userNotFound": MessageLookupByLibrary.simpleMessage("User Not Found!!"),
    "userNotLive": MessageLookupByLibrary.simpleMessage("User not live"),
    "username": MessageLookupByLibrary.simpleMessage("UserName"),
    "users": MessageLookupByLibrary.simpleMessage("Users"),
    "validEmail": MessageLookupByLibrary.simpleMessage("Enter Valid Email"),
    "verification": MessageLookupByLibrary.simpleMessage(
      "Apply for Verification",
    ),
    "verifiedAccountsHaveBlueEtc": MessageLookupByLibrary.simpleMessage(
      "Verified accounts have blue checkmarks next to their names to show that we have confirmed they are the real presence of the public figures or celebrities.",
    ),
    "versionText": MessageLookupByLibrary.simpleMessage("Version 1.0.0"),
    "video": MessageLookupByLibrary.simpleMessage("Video?"),
    "videoCall": MessageLookupByLibrary.simpleMessage("Video Call"),
    "videoCap": MessageLookupByLibrary.simpleMessage("VIDEO"),
    "videoDurationIs": MessageLookupByLibrary.simpleMessage(
      "Video duration is",
    ),
    "videoPreview": MessageLookupByLibrary.simpleMessage(
      "Video Preview Screen",
    ),
    "videos": MessageLookupByLibrary.simpleMessage("Videos"),
    "view": MessageLookupByLibrary.simpleMessage("View"),
    "viewers": MessageLookupByLibrary.simpleMessage("Viewers"),
    "walking": MessageLookupByLibrary.simpleMessage("Walking"),
    "wallet": MessageLookupByLibrary.simpleMessage("DIAMOND WALLET"),
    "walletCap": MessageLookupByLibrary.simpleMessage("WALLET"),
    "whatDoYouWantToSelect": MessageLookupByLibrary.simpleMessage(
      "What do you want to select?",
    ),
    "whereDoYouLive": MessageLookupByLibrary.simpleMessage(
      "WHERE DO YOU LIVE ?",
    ),
    "whichItemWouldYouLikeEtc": MessageLookupByLibrary.simpleMessage(
      "Which item would you like to select?\nSelect a item",
    ),
    "writeMessage": MessageLookupByLibrary.simpleMessage("Write Message"),
    "writeSomethingHere": MessageLookupByLibrary.simpleMessage(
      "Write something here...",
    ),
    "yes": MessageLookupByLibrary.simpleMessage("Yes"),
    "yesterday": MessageLookupByLibrary.simpleMessage("Yesterday"),
    "you": MessageLookupByLibrary.simpleMessage("you"),
    "youAreFakeUser": MessageLookupByLibrary.simpleMessage("You are fake User"),
    "youAreReporting": MessageLookupByLibrary.simpleMessage(
      "Your Are Reporting this user",
    ),
    "youBlockThisUser": MessageLookupByLibrary.simpleMessage(
      "You block this user",
    ),
    "youMust18": MessageLookupByLibrary.simpleMessage("You Must 18+"),
    "youMustBe18": MessageLookupByLibrary.simpleMessage("You must be 18+"),
    "youWillBeSentEtc": MessageLookupByLibrary.simpleMessage(
      "YOU WILL BE SENT OUTSIDE SOON",
    ),
    "yourApplicationIsPendingPleaseWait": MessageLookupByLibrary.simpleMessage(
      "Your Application is pending please wait",
    ),
    "yourLiveStreamHasBeenEndednbelowIsASummaryOf":
        MessageLookupByLibrary.simpleMessage(
          "Your call has been ended!\nBelow is a summary of it.",
        ),
    "yourSelfie": MessageLookupByLibrary.simpleMessage("YOUR SELFIE"),
    "youtube": MessageLookupByLibrary.simpleMessage("YOUTUBE"),
  };
}
