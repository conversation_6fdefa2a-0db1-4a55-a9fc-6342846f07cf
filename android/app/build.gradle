plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}


def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    compileSdk 35

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.mycompany.nurseminglelove"
        minSdkVersion 24
        targetSdkVersion 35
        multiDexEnabled true
        versionCode 8
        versionName "1.8"
//        ndk {
//            abiFilters 'armeabi-v7a', 'arm64-v8a'
//        }
    }

    // def keystoreProperties = new Properties()
    // def keystorePropertiesFile = rootProject.file("key.properties")
    // if (keystorePropertiesFile.exists()) {
    // keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
    // }

    // signingConfigs {
    // release {
    //     keyAlias keystoreProperties['keyAlias']
    //     keyPassword keystoreProperties['keyPassword']
    //     storeFile file(keystoreProperties['storeFile'])
    //     storePassword keystoreProperties['storePassword']
    // }
    // }   


    buildTypes {
        release {
            signingConfig signingConfigs.debug
        }
    }
    namespace 'com.mycompany.nurseminglelove'
}

flutter {
    source '../..'
}

dependencies {
    implementation platform('com.google.firebase:firebase-bom:33.10.0')
    implementation 'com.android.support:multidex:1.0.3'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'
}
