{"buildFiles": ["C:\\Users\\<USER>\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\wattlesol\\orange_flutter\\android\\app\\.cxx\\RelWithDebInfo\\5h1i526a\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\wattlesol\\orange_flutter\\android\\app\\.cxx\\RelWithDebInfo\\5h1i526a\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}