import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:orange_ui/model/subscription_plan.dart';
import 'package:orange_ui/screen/subscription_plan_screen/subscription_plan_screen.dart';

void main() {
  group('Subscription Plan Tests', () {
    testWidgets('SubscriptionPlan model creates default plans correctly', (
      WidgetTester tester,
    ) async {
      final plans = SubscriptionPlan.getDefaultPlans();

      expect(plans.length, 3);
      expect(plans[0].title, 'Free Plan');
      expect(plans[0].isFree, true);
      expect(plans[1].title, 'Gold Plan');
      expect(plans[1].isPopular, true);
      expect(plans[2].title, 'Platinum Plan');
    });

    testWidgets('SubscriptionPlanScreen renders without crashing', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        GetMaterialApp(home: const SubscriptionPlanScreen()),
      );

      expect(find.text('Choose Your Plan'), findsOneWidget);
      expect(find.text('Unlock Premium Features'), findsOneWidget);
      expect(find.text('Free Plan'), findsOneWidget);
      expect(find.text('Gold Plan'), findsOneWidget);
      expect(find.text('Platinum Plan'), findsOneWidget);
    });

    testWidgets('Plan cards display correct features', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        GetMaterialApp(home: const SubscriptionPlanScreen()),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      expect(
        find.text('Unlimited messaging'),
        findsNWidgets(3),
      ); // All plans have this
      expect(find.text('10 Likes per day'), findsOneWidget); // Only free plan
      expect(find.text('40 Likes per day'), findsOneWidget); // Only gold plan
      expect(
        find.text('Unlimited Likes'),
        findsOneWidget,
      ); // Only platinum plan
    });
  });
}
